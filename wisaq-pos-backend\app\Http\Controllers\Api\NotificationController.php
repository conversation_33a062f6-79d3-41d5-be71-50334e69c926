<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Notification;
use App\Models\Product;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class NotificationController extends Controller
{
    /**
     * عرض الإشعارات
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $companyId = Auth::user()->company_id;
            $perPage = $request->get('per_page', 15);
            
            $query = Notification::where(function($q) use ($userId, $companyId) {
                $q->where('user_id', $userId)
                  ->orWhere('company_id', $companyId);
            });

            // فلترة حسب النوع
            if ($request->has('type') && $request->type) {
                $query->where('type', $request->type);
            }

            // فلترة حسب الحالة
            if ($request->has('read_status')) {
                if ($request->read_status === 'read') {
                    $query->whereNotNull('read_at');
                } elseif ($request->read_status === 'unread') {
                    $query->whereNull('read_at');
                }
            }

            $notifications = $query->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => [
                    'notifications' => $notifications->items(),
                    'pagination' => [
                        'current_page' => $notifications->currentPage(),
                        'last_page' => $notifications->lastPage(),
                        'per_page' => $notifications->perPage(),
                        'total' => $notifications->total(),
                    ],
                    'unread_count' => Notification::where(function($q) use ($userId, $companyId) {
                        $q->where('user_id', $userId)
                          ->orWhere('company_id', $companyId);
                    })->whereNull('read_at')->count()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب الإشعارات'
            ], 500);
        }
    }

    /**
     * تحديد إشعار كمقروء
     */
    public function markAsRead($id): JsonResponse
    {
        try {
            $userId = Auth::id();
            $companyId = Auth::user()->company_id;
            
            $notification = Notification::where('id', $id)
                ->where(function($q) use ($userId, $companyId) {
                    $q->where('user_id', $userId)
                      ->orWhere('company_id', $companyId);
                })
                ->first();

            if (!$notification) {
                return response()->json([
                    'success' => false,
                    'message' => 'الإشعار غير موجود'
                ], 404);
            }

            $notification->update(['read_at' => now()]);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديد الإشعار كمقروء',
                'data' => $notification
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث الإشعار'
            ], 500);
        }
    }

    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    public function markAllAsRead(): JsonResponse
    {
        try {
            $userId = Auth::id();
            $companyId = Auth::user()->company_id;
            
            $updated = Notification::where(function($q) use ($userId, $companyId) {
                $q->where('user_id', $userId)
                  ->orWhere('company_id', $companyId);
            })
            ->whereNull('read_at')
            ->update(['read_at' => now()]);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديد جميع الإشعارات كمقروءة',
                'data' => ['updated_count' => $updated]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث الإشعارات'
            ], 500);
        }
    }

    /**
     * حذف إشعار
     */
    public function destroy($id): JsonResponse
    {
        try {
            $userId = Auth::id();
            $companyId = Auth::user()->company_id;
            
            $notification = Notification::where('id', $id)
                ->where(function($q) use ($userId, $companyId) {
                    $q->where('user_id', $userId)
                      ->orWhere('company_id', $companyId);
                })
                ->first();

            if (!$notification) {
                return response()->json([
                    'success' => false,
                    'message' => 'الإشعار غير موجود'
                ], 404);
            }

            $notification->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الإشعار بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في حذف الإشعار'
            ], 500);
        }
    }

    /**
     * إنشاء إشعارات تلقائية للمخزون المنخفض
     */
    public function generateLowStockNotifications(): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            
            // البحث عن المنتجات منخفضة المخزون
            $lowStockProducts = Product::where('company_id', $companyId)
                ->where('track_quantity', true)
                ->where('is_active', true)
                ->whereColumn('quantity', '<=', 'min_quantity')
                ->get();

            $notificationsCreated = 0;

            foreach ($lowStockProducts as $product) {
                // التحقق من عدم وجود إشعار مشابه في آخر 24 ساعة
                $existingNotification = Notification::where('company_id', $companyId)
                    ->where('type', 'low_stock')
                    ->where('data->product_id', $product->id)
                    ->where('created_at', '>=', now()->subDay())
                    ->first();

                if (!$existingNotification) {
                    Notification::create([
                        'company_id' => $companyId,
                        'type' => 'low_stock',
                        'title' => 'مخزون منخفض',
                        'message' => "المنتج '{$product->name}' وصل إلى الحد الأدنى للمخزون",
                        'data' => [
                            'product_id' => $product->id,
                            'product_name' => $product->name,
                            'current_quantity' => $product->quantity,
                            'min_quantity' => $product->min_quantity,
                        ],
                        'priority' => 'medium'
                    ]);
                    $notificationsCreated++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "تم إنشاء {$notificationsCreated} إشعار للمخزون المنخفض",
                'data' => [
                    'notifications_created' => $notificationsCreated,
                    'low_stock_products_count' => $lowStockProducts->count()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء إشعارات المخزون'
            ], 500);
        }
    }

    /**
     * إنشاء إشعارات انتهاء الصلاحية
     */
    public function generateExpiryNotifications(): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            
            // البحث عن المنتجات التي ستنتهي صلاحيتها خلال 30 يوم
            $expiringProducts = Product::where('company_id', $companyId)
                ->where('is_active', true)
                ->whereNotNull('expiry_date')
                ->where('expiry_date', '<=', now()->addDays(30))
                ->where('expiry_date', '>', now())
                ->get();

            $notificationsCreated = 0;

            foreach ($expiringProducts as $product) {
                $daysUntilExpiry = now()->diffInDays($product->expiry_date);
                
                // إنشاء إشعارات في أوقات محددة (30، 7، 3، 1 يوم)
                $notificationDays = [30, 7, 3, 1];
                
                if (in_array($daysUntilExpiry, $notificationDays)) {
                    // التحقق من عدم وجود إشعار مشابه
                    $existingNotification = Notification::where('company_id', $companyId)
                        ->where('type', 'expiry_warning')
                        ->where('data->product_id', $product->id)
                        ->where('data->days_until_expiry', $daysUntilExpiry)
                        ->first();

                    if (!$existingNotification) {
                        $priority = $daysUntilExpiry <= 3 ? 'high' : 'medium';
                        
                        Notification::create([
                            'company_id' => $companyId,
                            'type' => 'expiry_warning',
                            'title' => 'تحذير انتهاء صلاحية',
                            'message' => "المنتج '{$product->name}' سينتهي خلال {$daysUntilExpiry} يوم",
                            'data' => [
                                'product_id' => $product->id,
                                'product_name' => $product->name,
                                'expiry_date' => $product->expiry_date,
                                'days_until_expiry' => $daysUntilExpiry,
                            ],
                            'priority' => $priority
                        ]);
                        $notificationsCreated++;
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => "تم إنشاء {$notificationsCreated} إشعار لانتهاء الصلاحية",
                'data' => [
                    'notifications_created' => $notificationsCreated,
                    'expiring_products_count' => $expiringProducts->count()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء إشعارات انتهاء الصلاحية'
            ], 500);
        }
    }

    /**
     * إنشاء إشعارات الاشتراك
     */
    public function generateSubscriptionNotifications(): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $company = Company::with('subscription')->find($companyId);

            if (!$company || !$company->subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يوجد اشتراك نشط'
                ], 404);
            }

            $subscription = $company->subscription;
            $daysUntilExpiry = now()->diffInDays($subscription->expires_at, false);
            
            $notificationsCreated = 0;

            // إشعارات انتهاء الاشتراك (30، 7، 3، 1 يوم)
            $notificationDays = [30, 7, 3, 1];
            
            if ($daysUntilExpiry >= 0 && in_array($daysUntilExpiry, $notificationDays)) {
                // التحقق من عدم وجود إشعار مشابه
                $existingNotification = Notification::where('company_id', $companyId)
                    ->where('type', 'subscription_expiry')
                    ->where('data->days_until_expiry', $daysUntilExpiry)
                    ->whereDate('created_at', today())
                    ->first();

                if (!$existingNotification) {
                    $priority = $daysUntilExpiry <= 3 ? 'high' : 'medium';
                    
                    Notification::create([
                        'company_id' => $companyId,
                        'type' => 'subscription_expiry',
                        'title' => 'تحذير انتهاء الاشتراك',
                        'message' => "اشتراكك سينتهي خلال {$daysUntilExpiry} يوم. يرجى التجديد لتجنب انقطاع الخدمة",
                        'data' => [
                            'subscription_id' => $subscription->id,
                            'expires_at' => $subscription->expires_at,
                            'days_until_expiry' => $daysUntilExpiry,
                            'plan_name' => $subscription->plan->name ?? 'الخطة الأساسية',
                        ],
                        'priority' => $priority
                    ]);
                    $notificationsCreated++;
                }
            }

            // إشعار انتهاء الاشتراك
            if ($daysUntilExpiry < 0) {
                $existingNotification = Notification::where('company_id', $companyId)
                    ->where('type', 'subscription_expired')
                    ->whereDate('created_at', today())
                    ->first();

                if (!$existingNotification) {
                    Notification::create([
                        'company_id' => $companyId,
                        'type' => 'subscription_expired',
                        'title' => 'انتهى الاشتراك',
                        'message' => 'انتهى اشتراكك. يرجى التجديد فوراً لاستمرار الخدمة',
                        'data' => [
                            'subscription_id' => $subscription->id,
                            'expired_at' => $subscription->expires_at,
                            'days_since_expiry' => abs($daysUntilExpiry),
                        ],
                        'priority' => 'high'
                    ]);
                    $notificationsCreated++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "تم إنشاء {$notificationsCreated} إشعار للاشتراك",
                'data' => [
                    'notifications_created' => $notificationsCreated,
                    'subscription_status' => $subscription->status,
                    'days_until_expiry' => $daysUntilExpiry
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء إشعارات الاشتراك'
            ], 500);
        }
    }
}
