{"$schema": "https://json.schemastore.org/package.json", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/vite": "^4.0.0", "axios": "^1.10.0", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "tailwindcss": "^4.1.11", "vite": "^6.2.4"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.83.0", "autoprefixer": "^10.4.21", "clsx": "^2.1.1", "date-fns": "^4.1.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "postcss": "^8.5.6", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.5.2", "react-i18next": "^15.6.0", "react-router-dom": "^7.6.3", "recharts": "^3.1.0", "yup": "^1.6.1"}}