<?php

return [
    /*
    |--------------------------------------------------------------------------
    | IP Security Configuration
    |--------------------------------------------------------------------------
    |
    | Configure IP whitelisting and blacklisting for enhanced security
    |
    */

    'ip_whitelist' => [
        // Add trusted IP addresses here
        // '*************',
        // '*********',
    ],

    'ip_blacklist' => [
        // Add blocked IP addresses here
        // '*************',
        // '**********',
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting Configuration
    |--------------------------------------------------------------------------
    |
    | Configure rate limiting settings for different endpoints
    |
    */

    'rate_limits' => [
        'api' => [
            'max_attempts' => 120,
            'decay_minutes' => 1,
        ],
        'auth' => [
            'max_attempts' => 5,
            'decay_minutes' => 15,
        ],
        'password_reset' => [
            'max_attempts' => 3,
            'decay_minutes' => 60,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Headers Configuration
    |--------------------------------------------------------------------------
    |
    | Configure security headers that will be added to responses
    |
    */

    'headers' => [
        'x_content_type_options' => 'nosniff',
        'x_frame_options' => 'DENY',
        'x_xss_protection' => '1; mode=block',
        'referrer_policy' => 'strict-origin-when-cross-origin',
        'content_security_policy' => "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; media-src 'self'; object-src 'none'; child-src 'none'; worker-src 'none'; frame-ancestors 'none'; form-action 'self'; base-uri 'self'; manifest-src 'self'",
    ],

    /*
    |--------------------------------------------------------------------------
    | Audit Log Configuration
    |--------------------------------------------------------------------------
    |
    | Configure audit logging settings
    |
    */

    'audit_log' => [
        'enabled' => env('AUDIT_LOG_ENABLED', true),
        'log_failed_attempts' => true,
        'log_successful_logins' => true,
        'retention_days' => 90,
        'excluded_routes' => [
            'api/auth/refresh',
            'api/heartbeat',
            'api/health-check',
            'api/ping',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | XSS Protection Configuration
    |--------------------------------------------------------------------------
    |
    | Configure XSS protection settings
    |
    */

    'xss_protection' => [
        'enabled' => true,
        'allowed_tags' => '<p><br><strong><em><u><ol><ul><li><h1><h2><h3><h4><h5><h6>',
        'strip_dangerous_protocols' => true,
        'remove_event_handlers' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | SQL Injection Protection Configuration
    |--------------------------------------------------------------------------
    |
    | Configure SQL injection protection settings
    |
    */

    'sql_injection_protection' => [
        'enabled' => true,
        'log_attempts' => true,
        'block_suspicious_queries' => true,
        'sensitivity_level' => 'high', // low, medium, high
    ],

    /*
    |--------------------------------------------------------------------------
    | CSRF Protection Configuration
    |--------------------------------------------------------------------------
    |
    | Configure CSRF protection settings
    |
    */

    'csrf_protection' => [
        'enabled' => true,
        'check_origin' => true,
        'allowed_origins' => [
            env('APP_URL'),
            'http://localhost:3000',
            'http://127.0.0.1:3000',
        ],
        'token_lifetime' => 120, // minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Encryption Configuration
    |--------------------------------------------------------------------------
    |
    | Configure encryption settings for sensitive data
    |
    */

    'encryption' => [
        'algorithm' => 'AES-256-CBC',
        'key_rotation_days' => 90,
        'encrypt_audit_logs' => false,
        'encrypt_user_data' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Security Configuration
    |--------------------------------------------------------------------------
    |
    | Configure session security settings
    |
    */

    'session' => [
        'secure_cookies' => env('SESSION_SECURE_COOKIES', true),
        'http_only_cookies' => true,
        'same_site_cookies' => 'strict',
        'session_timeout' => 120, // minutes
        'regenerate_on_login' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Password Security Configuration
    |--------------------------------------------------------------------------
    |
    | Configure password security requirements
    |
    */

    'password' => [
        'min_length' => 8,
        'require_uppercase' => true,
        'require_lowercase' => true,
        'require_numbers' => true,
        'require_symbols' => true,
        'max_age_days' => 90,
        'prevent_reuse_count' => 5,
        'lockout_attempts' => 5,
        'lockout_duration' => 30, // minutes
    ],
];
