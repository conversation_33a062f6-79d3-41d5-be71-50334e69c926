import React, { useState } from 'react';
import {
  BellIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XMarkIcon,
  EyeIcon,
  TrashIcon,
} from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';
import { useApi } from '../hooks/useApi';
import { apiService } from '../services/api';
import toast from 'react-hot-toast';

const Notifications: React.FC = () => {
  const { isRTL } = useTheme();
  const [filter, setFilter] = useState('all');
  const [selectedNotifications, setSelectedNotifications] = useState<number[]>([]);

  const { data: notifications, loading, refetch } = useApi(
    () => apiService.getNotifications({ type: filter !== 'all' ? filter : undefined }),
    { immediate: true }
  );

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'low_stock':
        return ExclamationTriangleIcon;
      case 'expiry_warning':
        return ExclamationTriangleIcon;
      case 'subscription_expiry':
        return BellIcon;
      case 'subscription_expired':
        return ExclamationTriangleIcon;
      case 'info':
        return InformationCircleIcon;
      case 'success':
        return CheckCircleIcon;
      default:
        return BellIcon;
    }
  };

  const getNotificationColor = (type: string, priority: string) => {
    if (priority === 'high') {
      return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-200';
    }
    
    switch (type) {
      case 'low_stock':
      case 'expiry_warning':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900 dark:text-orange-200';
      case 'subscription_expiry':
      case 'subscription_expired':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-200';
      case 'success':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-200';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'low_stock':
        return isRTL ? 'مخزون منخفض' : 'Low Stock';
      case 'expiry_warning':
        return isRTL ? 'انتهاء صلاحية' : 'Expiry Warning';
      case 'subscription_expiry':
        return isRTL ? 'انتهاء اشتراك' : 'Subscription Expiry';
      case 'subscription_expired':
        return isRTL ? 'اشتراك منتهي' : 'Subscription Expired';
      case 'info':
        return isRTL ? 'معلومات' : 'Information';
      case 'success':
        return isRTL ? 'نجح' : 'Success';
      default:
        return type;
    }
  };

  const handleMarkAsRead = async (id: number) => {
    try {
      await apiService.markNotificationAsRead(id);
      refetch();
      toast.success(isRTL ? 'تم تحديد الإشعار كمقروء' : 'Notification marked as read');
    } catch (error) {
      toast.error(isRTL ? 'خطأ في تحديث الإشعار' : 'Error updating notification');
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await apiService.markAllNotificationsAsRead();
      refetch();
      toast.success(isRTL ? 'تم تحديد جميع الإشعارات كمقروءة' : 'All notifications marked as read');
    } catch (error) {
      toast.error(isRTL ? 'خطأ في تحديث الإشعارات' : 'Error updating notifications');
    }
  };

  const handleDelete = async (id: number) => {
    if (window.confirm(isRTL ? 'هل أنت متأكد من حذف هذا الإشعار؟' : 'Are you sure you want to delete this notification?')) {
      try {
        await apiService.deleteNotification(id);
        refetch();
        toast.success(isRTL ? 'تم حذف الإشعار' : 'Notification deleted');
      } catch (error) {
        toast.error(isRTL ? 'خطأ في حذف الإشعار' : 'Error deleting notification');
      }
    }
  };

  const handleSelectNotification = (id: number) => {
    setSelectedNotifications(prev => 
      prev.includes(id) 
        ? prev.filter(nId => nId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedNotifications.length === notifications?.notifications?.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(notifications?.notifications?.map((n: any) => n.id) || []);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedNotifications.length === 0) return;
    
    if (window.confirm(isRTL ? `هل أنت متأكد من حذف ${selectedNotifications.length} إشعار؟` : `Are you sure you want to delete ${selectedNotifications.length} notifications?`)) {
      try {
        await Promise.all(selectedNotifications.map(id => apiService.deleteNotification(id)));
        refetch();
        setSelectedNotifications([]);
        toast.success(isRTL ? 'تم حذف الإشعارات المحددة' : 'Selected notifications deleted');
      } catch (error) {
        toast.error(isRTL ? 'خطأ في حذف الإشعارات' : 'Error deleting notifications');
      }
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-48 animate-pulse"></div>
        <div className="space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="card p-4 animate-pulse">
              <div className="flex items-center space-x-3">
                <div className="h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {isRTL ? 'الإشعارات' : 'Notifications'}
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {isRTL ? 'إدارة جميع إشعارات النظام' : 'Manage all system notifications'}
          </p>
        </div>
        <div className="flex items-center gap-3">
          {notifications?.unread_count > 0 && (
            <button
              onClick={handleMarkAllAsRead}
              className="btn-secondary"
            >
              {isRTL ? 'تحديد الكل كمقروء' : 'Mark All as Read'}
            </button>
          )}
          {selectedNotifications.length > 0 && (
            <button
              onClick={handleBulkDelete}
              className="btn-danger"
            >
              {isRTL ? `حذف المحدد (${selectedNotifications.length})` : `Delete Selected (${selectedNotifications.length})`}
            </button>
          )}
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-blue-100 dark:bg-blue-900 p-2 rounded-lg">
              <BellIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div className={`${isRTL ? 'mr-3' : 'ml-3'}`}>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isRTL ? 'إجمالي الإشعارات' : 'Total Notifications'}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {notifications?.pagination?.total || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-orange-100 dark:bg-orange-900 p-2 rounded-lg">
              <ExclamationTriangleIcon className="h-5 w-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div className={`${isRTL ? 'mr-3' : 'ml-3'}`}>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isRTL ? 'غير مقروءة' : 'Unread'}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {notifications?.unread_count || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-red-100 dark:bg-red-900 p-2 rounded-lg">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
            <div className={`${isRTL ? 'mr-3' : 'ml-3'}`}>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isRTL ? 'عالية الأولوية' : 'High Priority'}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {notifications?.notifications?.filter((n: any) => n.priority === 'high').length || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-4">
          <div className="flex items-center">
            <div className="bg-green-100 dark:bg-green-900 p-2 rounded-lg">
              <CheckCircleIcon className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
            <div className={`${isRTL ? 'mr-3' : 'ml-3'}`}>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isRTL ? 'مقروءة' : 'Read'}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {(notifications?.pagination?.total || 0) - (notifications?.unread_count || 0)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="card p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={selectedNotifications.length === notifications?.notifications?.length && notifications?.notifications?.length > 0}
                onChange={handleSelectAll}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className={`${isRTL ? 'mr-2' : 'ml-2'} text-sm text-gray-700 dark:text-gray-300`}>
                {isRTL ? 'تحديد الكل' : 'Select All'}
              </span>
            </label>
          </div>
          
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="input-field w-auto"
          >
            <option value="all">{isRTL ? 'جميع الإشعارات' : 'All Notifications'}</option>
            <option value="low_stock">{isRTL ? 'مخزون منخفض' : 'Low Stock'}</option>
            <option value="expiry_warning">{isRTL ? 'انتهاء صلاحية' : 'Expiry Warning'}</option>
            <option value="subscription_expiry">{isRTL ? 'انتهاء اشتراك' : 'Subscription Expiry'}</option>
            <option value="info">{isRTL ? 'معلومات' : 'Information'}</option>
          </select>
        </div>
      </div>

      {/* Notifications List */}
      <div className="space-y-3">
        {notifications?.notifications?.map((notification: any) => {
          const IconComponent = getNotificationIcon(notification.type);
          const isUnread = !notification.read_at;
          
          return (
            <div
              key={notification.id}
              className={`card p-4 transition-colors ${
                isUnread ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800' : ''
              } ${
                selectedNotifications.includes(notification.id) ? 'ring-2 ring-primary-500' : ''
              }`}
            >
              <div className="flex items-start gap-4">
                <input
                  type="checkbox"
                  checked={selectedNotifications.includes(notification.id)}
                  onChange={() => handleSelectNotification(notification.id)}
                  className="mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                
                <div className={`flex-shrink-0 p-2 rounded-lg ${getNotificationColor(notification.type, notification.priority)}`}>
                  <IconComponent className="h-5 w-5" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <h3 className={`text-sm font-medium ${isUnread ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'}`}>
                      {notification.title}
                    </h3>
                    <div className="flex items-center gap-2">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getNotificationColor(notification.type, notification.priority)}`}>
                        {getTypeText(notification.type)}
                      </span>
                      {notification.priority === 'high' && (
                        <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                          {isRTL ? 'عاجل' : 'Urgent'}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <p className={`text-sm ${isUnread ? 'text-gray-700 dark:text-gray-300' : 'text-gray-500 dark:text-gray-400'}`}>
                    {notification.message}
                  </p>
                  
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {formatDate(notification.created_at)}
                    </span>
                    
                    <div className="flex items-center gap-2">
                      {isUnread && (
                        <button
                          onClick={() => handleMarkAsRead(notification.id)}
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                          title={isRTL ? 'تحديد كمقروء' : 'Mark as read'}
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                      )}
                      
                      <button
                        onClick={() => handleDelete(notification.id)}
                        className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                        title={isRTL ? 'حذف' : 'Delete'}
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Empty State */}
      {!loading && (!notifications?.notifications || notifications.notifications.length === 0) && (
        <div className="text-center py-12">
          <BellIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
            {isRTL ? 'لا توجد إشعارات' : 'No notifications'}
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {isRTL ? 'لا توجد إشعارات لعرضها حالياً' : 'No notifications to display at the moment'}
          </p>
        </div>
      )}
    </div>
  );
};

export default Notifications;
