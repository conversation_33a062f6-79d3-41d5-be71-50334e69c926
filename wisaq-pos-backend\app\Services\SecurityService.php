<?php

namespace App\Services;

use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SecurityService
{
    /**
     * Encrypt sensitive data
     */
    public function encryptSensitiveData(string $data): string
    {
        try {
            return Crypt::encryptString($data);
        } catch (\Exception $e) {
            Log::error('Failed to encrypt sensitive data: ' . $e->getMessage());
            throw new \Exception('فشل في تشفير البيانات الحساسة');
        }
    }

    /**
     * Decrypt sensitive data
     */
    public function decryptSensitiveData(string $encryptedData): string
    {
        try {
            return Crypt::decryptString($encryptedData);
        } catch (\Exception $e) {
            Log::error('Failed to decrypt sensitive data: ' . $e->getMessage());
            throw new \Exception('فشل في فك تشفير البيانات الحساسة');
        }
    }

    /**
     * Generate secure random token
     */
    public function generateSecureToken(int $length = 32): string
    {
        return Str::random($length);
    }

    /**
     * Generate secure API key
     */
    public function generateApiKey(): string
    {
        return 'wq_' . Str::random(40);
    }

    /**
     * Hash password with additional security
     */
    public function hashPassword(string $password): string
    {
        return Hash::make($password, [
            'rounds' => 12, // Higher cost for better security
        ]);
    }

    /**
     * Verify password hash
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return Hash::check($password, $hash);
    }

    /**
     * Generate secure OTP
     */
    public function generateOTP(int $length = 6): string
    {
        $digits = '0123456789';
        $otp = '';
        
        for ($i = 0; $i < $length; $i++) {
            $otp .= $digits[random_int(0, strlen($digits) - 1)];
        }
        
        return $otp;
    }

    /**
     * Validate password strength
     */
    public function validatePasswordStrength(string $password): array
    {
        $config = config('security.password');
        $errors = [];

        // Check minimum length
        if (strlen($password) < $config['min_length']) {
            $errors[] = "كلمة المرور يجب أن تكون على الأقل {$config['min_length']} أحرف";
        }

        // Check for uppercase letter
        if ($config['require_uppercase'] && !preg_match('/[A-Z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
        }

        // Check for lowercase letter
        if ($config['require_lowercase'] && !preg_match('/[a-z]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
        }

        // Check for numbers
        if ($config['require_numbers'] && !preg_match('/[0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
        }

        // Check for symbols
        if ($config['require_symbols'] && !preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'strength' => $this->calculatePasswordStrength($password)
        ];
    }

    /**
     * Calculate password strength score
     */
    protected function calculatePasswordStrength(string $password): string
    {
        $score = 0;
        $length = strlen($password);

        // Length score
        if ($length >= 8) $score += 1;
        if ($length >= 12) $score += 1;
        if ($length >= 16) $score += 1;

        // Character variety score
        if (preg_match('/[a-z]/', $password)) $score += 1;
        if (preg_match('/[A-Z]/', $password)) $score += 1;
        if (preg_match('/[0-9]/', $password)) $score += 1;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 1;

        // Pattern checks
        if (!preg_match('/(.)\1{2,}/', $password)) $score += 1; // No repeated characters
        if (!preg_match('/123|abc|qwe|asd/i', $password)) $score += 1; // No common patterns

        switch (true) {
            case $score >= 8:
                return 'قوية جداً';
            case $score >= 6:
                return 'قوية';
            case $score >= 4:
                return 'متوسطة';
            case $score >= 2:
                return 'ضعيفة';
            default:
                return 'ضعيفة جداً';
        }
    }

    /**
     * Sanitize input for security
     */
    public function sanitizeInput(string $input): string
    {
        // Remove null bytes
        $input = str_replace("\0", '', $input);
        
        // Remove control characters except tab, newline, and carriage return
        $input = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $input);
        
        // Trim whitespace
        $input = trim($input);
        
        return $input;
    }

    /**
     * Generate CSRF token
     */
    public function generateCSRFToken(): string
    {
        return hash('sha256', Str::random(40) . time());
    }

    /**
     * Verify CSRF token
     */
    public function verifyCSRFToken(string $token, string $sessionToken): bool
    {
        return hash_equals($sessionToken, $token);
    }

    /**
     * Generate secure file name
     */
    public function generateSecureFileName(string $originalName): string
    {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $safeName = preg_replace('/[^a-zA-Z0-9._-]/', '', $originalName);
        $timestamp = time();
        $random = Str::random(8);
        
        return "{$timestamp}_{$random}_{$safeName}";
    }

    /**
     * Validate file upload security
     */
    public function validateFileUpload(string $fileName, string $mimeType, int $fileSize): array
    {
        $errors = [];
        
        // Check file extension
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'];
        $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        if (!in_array($extension, $allowedExtensions)) {
            $errors[] = 'نوع الملف غير مسموح';
        }
        
        // Check MIME type
        $allowedMimeTypes = [
            'image/jpeg', 'image/png', 'image/gif',
            'application/pdf',
            'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        ];
        
        if (!in_array($mimeType, $allowedMimeTypes)) {
            $errors[] = 'نوع الملف غير صحيح';
        }
        
        // Check file size (10MB max)
        $maxSize = 10 * 1024 * 1024;
        if ($fileSize > $maxSize) {
            $errors[] = 'حجم الملف كبير جداً (الحد الأقصى 10 ميجابايت)';
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Log security event
     */
    public function logSecurityEvent(string $event, array $data = []): void
    {
        Log::channel('security')->info($event, array_merge($data, [
            'timestamp' => now(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]));
    }
}
