import React, { useState } from 'react';
import {
  CreditCardIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  CalendarDaysIcon,
  BuildingOfficeIcon,
  UsersIcon,
  CubeIcon,
  ArrowUpIcon,
} from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';
import { useApi } from '../hooks/useApi';
import { apiService } from '../services/api';
import toast from 'react-hot-toast';

const Subscription: React.FC = () => {
  const { isRTL } = useTheme();
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<any>(null);

  const { data: currentSubscription, loading: subscriptionLoading, refetch: refetchSubscription } = useApi(
    () => apiService.getCurrentSubscription(),
    { immediate: true }
  );

  const { data: plans, loading: plansLoading } = useApi(
    () => apiService.getSubscriptionPlans(),
    { immediate: true }
  );

  const { data: paymentHistory, loading: historyLoading } = useApi(
    () => apiService.getPaymentHistory(),
    { immediate: true }
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(isRTL ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-200';
      case 'expired':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-200';
      case 'cancelled':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900 dark:text-orange-200';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return isRTL ? 'نشط' : 'Active';
      case 'expired':
        return isRTL ? 'منتهي' : 'Expired';
      case 'cancelled':
        return isRTL ? 'ملغي' : 'Cancelled';
      default:
        return status;
    }
  };

  const handleUpgrade = async (planId: number) => {
    try {
      const response = await apiService.upgradeSubscription(planId);
      if (response.success) {
        toast.success(isRTL ? 'تم ترقية الاشتراك بنجاح' : 'Subscription upgraded successfully');
        refetchSubscription();
        setShowUpgradeModal(false);
      }
    } catch (error) {
      toast.error(isRTL ? 'خطأ في ترقية الاشتراك' : 'Error upgrading subscription');
    }
  };

  const handleRenew = async () => {
    try {
      const response = await apiService.renewSubscription();
      if (response.success) {
        // Redirect to payment
        const paymentResponse = await apiService.createPayment({
          amount: response.data.billing_details.total_cost,
          currency: 'SAR',
          description: 'تجديد الاشتراك الشهري',
          subscription_id: response.data.subscription.id,
          customer_email: '<EMAIL>', // Get from user context
          customer_phone: '966500000000', // Get from user context
          redirect_url: window.location.origin + '/subscription?payment=success'
        });

        if (paymentResponse.success) {
          window.location.href = paymentResponse.data.payment_url;
        }
      }
    } catch (error) {
      toast.error(isRTL ? 'خطأ في تجديد الاشتراك' : 'Error renewing subscription');
    }
  };

  const handleCancel = async () => {
    if (window.confirm(isRTL ? 'هل أنت متأكد من إلغاء الاشتراك؟' : 'Are you sure you want to cancel the subscription?')) {
      try {
        const response = await apiService.cancelSubscription();
        if (response.success) {
          toast.success(response.message);
          refetchSubscription();
        }
      } catch (error) {
        toast.error(isRTL ? 'خطأ في إلغاء الاشتراك' : 'Error cancelling subscription');
      }
    }
  };

  if (subscriptionLoading) {
    return (
      <div className="space-y-6">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-48 animate-pulse"></div>
        <div className="card p-6 animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          {isRTL ? 'إدارة الاشتراك' : 'Subscription Management'}
        </h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          {isRTL ? 'إدارة خطة الاشتراك والمدفوعات' : 'Manage your subscription plan and payments'}
        </p>
      </div>

      {/* Current Subscription */}
      <div className="card p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            {isRTL ? 'الاشتراك الحالي' : 'Current Subscription'}
          </h2>
          <span className={`inline-flex px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(currentSubscription?.subscription?.status || 'inactive')}`}>
            {getStatusText(currentSubscription?.subscription?.status || 'inactive')}
          </span>
        </div>

        {currentSubscription && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div className="text-center">
              <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-lg inline-flex mb-2">
                <CreditCardIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isRTL ? 'الخطة الحالية' : 'Current Plan'}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {currentSubscription.plan?.name || (isRTL ? 'الخطة الأساسية' : 'Basic Plan')}
              </p>
            </div>

            <div className="text-center">
              <div className="bg-green-100 dark:bg-green-900 p-3 rounded-lg inline-flex mb-2">
                <CalendarDaysIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isRTL ? 'تاريخ التجديد' : 'Next Billing'}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {currentSubscription.billing?.next_billing_date 
                  ? formatDate(currentSubscription.billing.next_billing_date)
                  : '-'
                }
              </p>
            </div>

            <div className="text-center">
              <div className="bg-purple-100 dark:bg-purple-900 p-3 rounded-lg inline-flex mb-2">
                <BuildingOfficeIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isRTL ? 'الفروع المستخدمة' : 'Branches Used'}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {currentSubscription.current_usage?.branches || 0} / {currentSubscription.limits?.max_branches || '∞'}
              </p>
            </div>

            <div className="text-center">
              <div className="bg-orange-100 dark:bg-orange-900 p-3 rounded-lg inline-flex mb-2">
                <CreditCardIcon className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isRTL ? 'التكلفة الشهرية' : 'Monthly Cost'}
              </p>
              <p className="text-lg font-semibold text-gray-900 dark:text-white">
                {formatCurrency(currentSubscription.billing?.total_monthly_cost || 0)}
              </p>
            </div>
          </div>
        )}

        {/* Usage Details */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
          <h3 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            {isRTL ? 'تفاصيل الاستخدام' : 'Usage Details'}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center">
                <UsersIcon className="h-5 w-5 text-gray-400 mr-2" />
                <span className="text-sm text-gray-600 dark:text-gray-300">
                  {isRTL ? 'المستخدمين' : 'Users'}
                </span>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {currentSubscription?.current_usage?.users || 0}
              </span>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center">
                <CubeIcon className="h-5 w-5 text-gray-400 mr-2" />
                <span className="text-sm text-gray-600 dark:text-gray-300">
                  {isRTL ? 'المنتجات' : 'Products'}
                </span>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {currentSubscription?.current_usage?.products || 0}
              </span>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <div className="flex items-center">
                <CreditCardIcon className="h-5 w-5 text-gray-400 mr-2" />
                <span className="text-sm text-gray-600 dark:text-gray-300">
                  {isRTL ? 'المبيعات الشهرية' : 'Monthly Sales'}
                </span>
              </div>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {currentSubscription?.current_usage?.monthly_sales || 0}
              </span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="border-t border-gray-200 dark:border-gray-700 pt-6 flex flex-wrap gap-3">
          {currentSubscription?.billing?.is_expired && (
            <button
              onClick={handleRenew}
              className="btn-primary flex items-center gap-2"
            >
              <CreditCardIcon className="h-5 w-5" />
              {isRTL ? 'تجديد الاشتراك' : 'Renew Subscription'}
            </button>
          )}

          {currentSubscription?.billing?.is_expiring_soon && !currentSubscription?.billing?.is_expired && (
            <button
              onClick={handleRenew}
              className="btn-primary flex items-center gap-2"
            >
              <ExclamationTriangleIcon className="h-5 w-5" />
              {isRTL ? 'تجديد مبكر' : 'Early Renewal'}
            </button>
          )}

          <button
            onClick={() => setShowUpgradeModal(true)}
            className="btn-secondary flex items-center gap-2"
          >
            <ArrowUpIcon className="h-5 w-5" />
            {isRTL ? 'ترقية الخطة' : 'Upgrade Plan'}
          </button>

          {currentSubscription?.subscription?.status === 'active' && (
            <button
              onClick={handleCancel}
              className="btn-danger flex items-center gap-2"
            >
              {isRTL ? 'إلغاء الاشتراك' : 'Cancel Subscription'}
            </button>
          )}
        </div>
      </div>

      {/* Available Plans */}
      {showUpgradeModal && (
        <div className="card p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
            {isRTL ? 'الخطط المتاحة' : 'Available Plans'}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {plans?.map((plan: any) => (
              <div
                key={plan.id}
                className={`border rounded-lg p-6 cursor-pointer transition-colors ${
                  selectedPlan?.id === plan.id
                    ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                    : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                }`}
                onClick={() => setSelectedPlan(plan)}
              >
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {plan.name}
                  </h3>
                  <p className="text-3xl font-bold text-primary-600 dark:text-primary-400 mt-2">
                    {formatCurrency(plan.price)}
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      /{isRTL ? 'شهر' : 'month'}
                    </span>
                  </p>
                  <ul className="mt-4 space-y-2 text-sm text-gray-600 dark:text-gray-300">
                    <li className="flex items-center">
                      <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                      {plan.max_branches} {isRTL ? 'فرع' : 'branches'}
                    </li>
                    <li className="flex items-center">
                      <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                      {plan.max_users} {isRTL ? 'مستخدم' : 'users'}
                    </li>
                    <li className="flex items-center">
                      <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                      {plan.max_products} {isRTL ? 'منتج' : 'products'}
                    </li>
                  </ul>
                  {selectedPlan?.id === plan.id && (
                    <button
                      onClick={() => handleUpgrade(plan.id)}
                      className="btn-primary w-full mt-4"
                    >
                      {isRTL ? 'ترقية إلى هذه الخطة' : 'Upgrade to this Plan'}
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
          <div className="mt-6 flex justify-end">
            <button
              onClick={() => setShowUpgradeModal(false)}
              className="btn-secondary"
            >
              {isRTL ? 'إلغاء' : 'Cancel'}
            </button>
          </div>
        </div>
      )}

      {/* Payment History */}
      <div className="card p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">
          {isRTL ? 'سجل المدفوعات' : 'Payment History'}
        </h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-800">
              <tr>
                <th className="table-header">{isRTL ? 'التاريخ' : 'Date'}</th>
                <th className="table-header">{isRTL ? 'المبلغ' : 'Amount'}</th>
                <th className="table-header">{isRTL ? 'الوصف' : 'Description'}</th>
                <th className="table-header">{isRTL ? 'الحالة' : 'Status'}</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
              {paymentHistory?.data?.map((payment: any) => (
                <tr key={payment.id}>
                  <td className="table-cell">{formatDate(payment.created_at)}</td>
                  <td className="table-cell">{formatCurrency(payment.amount)}</td>
                  <td className="table-cell">{payment.description}</td>
                  <td className="table-cell">
                    <span className={`badge ${getStatusColor(payment.status)}`}>
                      {getStatusText(payment.status)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Subscription;
