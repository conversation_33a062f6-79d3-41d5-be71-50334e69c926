<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Tymon\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements JWTSubject
{
    use HasFactory, Notifiable, HasRoles, SoftDeletes;

    /**
     * الحقول القابلة للتعبئة
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        'company_id',
        'branch_id',
        'user_type',
        'status',
        'avatar',
        'preferences',
        'last_login_at',
        'last_login_ip',
        'password_changed_at',
        'failed_login_attempts',
        'locked_until',
        'two_factor_enabled',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'force_password_change',
        'security_questions',
        'last_activity_at',
    ];

    /**
     * الحقول المخفية
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'security_questions',
    ];

    /**
     * تحويل الحقول
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'preferences' => 'array',
            'last_login_at' => 'datetime',
            'password_changed_at' => 'datetime',
            'locked_until' => 'datetime',
            'two_factor_enabled' => 'boolean',
            'two_factor_recovery_codes' => 'array',
            'force_password_change' => 'boolean',
            'security_questions' => 'array',
            'last_activity_at' => 'datetime',
        ];
    }

    /**
     * JWT Methods
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [
            'user_type' => $this->user_type,
            'company_id' => $this->company_id,
            'branch_id' => $this->branch_id,
        ];
    }

    /**
     * العلاقات
     */

    /**
     * الشركة التابع لها المستخدم
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    /**
     * الفرع التابع له المستخدم
     */
    public function branch(): BelongsTo
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * المبيعات التي قام بها المستخدم
     */
    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * سجلات المخزون التي قام بها المستخدم
     */
    public function inventoryLogs(): HasMany
    {
        return $this->hasMany(InventoryLog::class);
    }

    /**
     * التنبيهات الخاصة بالمستخدم
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * Scopes
     */

    /**
     * المستخدمين النشطين
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * المستخدمين حسب النوع
     */
    public function scopeByType($query, $type)
    {
        return $query->where('user_type', $type);
    }

    /**
     * المستخدمين في شركة معينة
     */
    public function scopeInCompany($query, $companyId)
    {
        return $query->where('company_id', $companyId);
    }

    /**
     * المستخدمين في فرع معين
     */
    public function scopeInBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    /**
     * Helper Methods
     */

    /**
     * هل المستخدم مدير عام؟
     */
    public function isSuperAdmin(): bool
    {
        return $this->user_type === 'super_admin';
    }

    /**
     * هل المستخدم مدير؟
     */
    public function isAdmin(): bool
    {
        return $this->user_type === 'admin';
    }

    /**
     * هل المستخدم مشرف؟
     */
    public function isSupervisor(): bool
    {
        return $this->user_type === 'supervisor';
    }

    /**
     * هل المستخدم مدير فرع؟
     */
    public function isManager(): bool
    {
        return $this->user_type === 'manager';
    }

    /**
     * هل المستخدم كاشير؟
     */
    public function isCashier(): bool
    {
        return $this->user_type === 'cashier';
    }

    /**
     * هل المستخدم محاسب؟
     */
    public function isAccountant(): bool
    {
        return $this->user_type === 'accountant';
    }

    /**
     * هل المستخدم نشط؟
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * تحديث آخر تسجيل دخول
     */
    public function updateLastLogin($ip = null): void
    {
        $this->update([
            'last_login_at' => now(),
            'last_login_ip' => $ip ?? request()->ip(),
        ]);
    }

    /**
     * الحصول على الاسم الكامل مع نوع المستخدم
     */
    public function getFullNameAttribute(): string
    {
        $types = [
            'super_admin' => 'مدير عام',
            'admin' => 'مدير',
            'supervisor' => 'مشرف',
            'manager' => 'مدير فرع',
            'cashier' => 'كاشير',
            'accountant' => 'محاسب',
        ];

        return $this->name . ' (' . ($types[$this->user_type] ?? $this->user_type) . ')';
    }

    /**
     * Security Methods
     */

    /**
     * هل المستخدم محظور؟
     */
    public function isLocked(): bool
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }

    /**
     * حظر المستخدم لفترة معينة
     */
    public function lockUser(int $minutes = 30): void
    {
        $this->update([
            'locked_until' => now()->addMinutes($minutes),
            'failed_login_attempts' => 0
        ]);
    }

    /**
     * إلغاء حظر المستخدم
     */
    public function unlockUser(): void
    {
        $this->update([
            'locked_until' => null,
            'failed_login_attempts' => 0
        ]);
    }

    /**
     * زيادة عدد محاولات تسجيل الدخول الفاشلة
     */
    public function incrementFailedAttempts(): void
    {
        $this->increment('failed_login_attempts');

        // حظر المستخدم بعد 5 محاولات فاشلة
        if ($this->failed_login_attempts >= 5) {
            $this->lockUser(30); // حظر لمدة 30 دقيقة
        }
    }

    /**
     * إعادة تعيين محاولات تسجيل الدخول الفاشلة
     */
    public function resetFailedAttempts(): void
    {
        $this->update(['failed_login_attempts' => 0]);
    }

    /**
     * هل يجب على المستخدم تغيير كلمة المرور؟
     */
    public function shouldChangePassword(): bool
    {
        if ($this->force_password_change) {
            return true;
        }

        // التحقق من عمر كلمة المرور
        $maxAge = config('security.password.max_age_days', 90);
        if ($this->password_changed_at) {
            return $this->password_changed_at->diffInDays(now()) >= $maxAge;
        }

        return false;
    }

    /**
     * هل المصادقة الثنائية مفعلة؟
     */
    public function hasTwoFactorEnabled(): bool
    {
        return $this->two_factor_enabled && !empty($this->two_factor_secret);
    }

    /**
     * تفعيل المصادقة الثنائية
     */
    public function enableTwoFactor(string $secret, array $recoveryCodes): void
    {
        $this->update([
            'two_factor_enabled' => true,
            'two_factor_secret' => encrypt($secret),
            'two_factor_recovery_codes' => array_map('encrypt', $recoveryCodes)
        ]);
    }

    /**
     * إلغاء تفعيل المصادقة الثنائية
     */
    public function disableTwoFactor(): void
    {
        $this->update([
            'two_factor_enabled' => false,
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null
        ]);
    }

    /**
     * تحديث آخر نشاط
     */
    public function updateLastActivity(): void
    {
        $this->update(['last_activity_at' => now()]);
    }

    /**
     * هل المستخدم نشط حالياً؟
     */
    public function isCurrentlyActive(): bool
    {
        if (!$this->last_activity_at) {
            return false;
        }

        // اعتبار المستخدم نشط إذا كان آخر نشاط خلال 15 دقيقة
        return $this->last_activity_at->diffInMinutes(now()) <= 15;
    }

    /**
     * الحصول على سجلات المراجعة للمستخدم
     */
    public function auditLogs(): HasMany
    {
        return $this->hasMany(AuditLog::class);
    }
}
