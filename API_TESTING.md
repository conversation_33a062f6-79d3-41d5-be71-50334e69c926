# اختبار API - نظام وثاق لنقاط البيع

## 🔗 Base URL
```
http://localhost:8000/api
```

## 🔐 تسجيل الدخول

### POST /auth/login
```bash
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Admin@123456"
  }'
```

**الاستجابة المتوقعة:**
```json
{
  "success": true,
  "message": "تم تسجيل الدخول بنجاح",
  "data": {
    "user": {
      "id": 1,
      "name": "مدير النظام",
      "email": "<EMAIL>",
      "user_type": "super_admin"
    },
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600
  }
}
```

## 👤 معلومات المستخدم

### GET /auth/me
```bash
curl -X GET http://localhost:8000/api/auth/me \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 🏢 إدارة الشركات

### GET /admin/companies
```bash
curl -X GET http://localhost:8000/api/admin/companies \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### POST /admin/companies
```bash
curl -X POST http://localhost:8000/api/admin/companies \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "شركة تجريبية",
    "email": "<EMAIL>",
    "phone": "+966501234567",
    "address": "الرياض، السعودية",
    "tax_number": "*********",
    "subscription_plan": "basic"
  }'
```

## 🏪 إدارة الفروع

### GET /branches
```bash
curl -X GET http://localhost:8000/api/branches \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### POST /branches
```bash
curl -X POST http://localhost:8000/api/branches \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "الفرع الرئيسي",
    "address": "شارع الملك فهد، الرياض",
    "phone": "+966501234567",
    "is_main": true
  }'
```

## 📦 إدارة المنتجات

### GET /products
```bash
curl -X GET http://localhost:8000/api/products \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### POST /products
```bash
curl -X POST http://localhost:8000/api/products \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "منتج تجريبي",
    "name_en": "Test Product",
    "description": "وصف المنتج",
    "barcode": "*********0123",
    "sku": "TEST001",
    "category_id": 1,
    "purchase_price": 50.00,
    "selling_price": 75.00,
    "quantity": 100,
    "min_quantity": 10,
    "unit": "قطعة",
    "status": "active"
  }'
```

### GET /products/barcode/{barcode}
```bash
curl -X GET http://localhost:8000/api/products/barcode/*********0123 \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 👥 إدارة العملاء

### GET /customers
```bash
curl -X GET http://localhost:8000/api/customers \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### POST /customers
```bash
curl -X POST http://localhost:8000/api/customers \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "عميل تجريبي",
    "email": "<EMAIL>",
    "phone": "+966501234567",
    "address": "الرياض، السعودية",
    "customer_type": "individual"
  }'
```

## 🛒 نقاط البيع (POS)

### POST /pos/create-sale
```bash
curl -X POST http://localhost:8000/api/pos/create-sale \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": 1,
    "items": [
      {
        "product_id": 1,
        "quantity": 2,
        "unit_price": 75.00
      }
    ],
    "payment_method": "cash",
    "tax_rate": 15,
    "discount_amount": 0
  }'
```

## 📊 التقارير

### GET /reports/dashboard
```bash
curl -X GET http://localhost:8000/api/reports/dashboard \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### GET /reports/sales-report
```bash
curl -X GET "http://localhost:8000/api/reports/sales-report?date_from=2024-01-01&date_to=2024-12-31" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

## 🔒 الأمان

### GET /security/dashboard
```bash
curl -X GET http://localhost:8000/api/security/dashboard \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### POST /security/change-password
```bash
curl -X POST http://localhost:8000/api/security/change-password \
  -H "Authorization: Bearer YOUR_TOKEN_HERE" \
  -H "Content-Type: application/json" \
  -d '{
    "current_password": "Admin@123456",
    "password": "NewPassword@123",
    "password_confirmation": "NewPassword@123"
  }'
```

## 📋 عرض جميع الـ Routes

```bash
cd wisaq-pos-backend
php artisan route:list
```

## 🧪 اختبار سريع بـ Postman

1. **استيراد Collection**: أنشئ Postman Collection جديدة
2. **إعداد Environment**: 
   - `base_url`: http://localhost:8000/api
   - `token`: (سيتم تعبئته بعد تسجيل الدخول)
3. **تسجيل الدخول أولاً**: احصل على الـ token
4. **استخدم الـ token**: في جميع الطلبات الأخرى

## ⚠️ ملاحظات مهمة

- **جميع الـ routes محمية**: تحتاج Authorization header
- **استخدم Content-Type**: application/json للـ POST requests
- **الاستجابات بالعربية**: جميع الرسائل تدعم العربية
- **Rate Limiting**: محدود بـ 120 طلب في الدقيقة
- **Validation**: جميع البيانات تخضع للتحقق

## 🔧 استكشاف الأخطاء

### خطأ 401 Unauthorized
- تأكد من وجود Authorization header
- تحقق من صحة الـ token
- تأكد من عدم انتهاء صلاحية الـ token

### خطأ 422 Validation Error
- تحقق من البيانات المرسلة
- تأكد من وجود جميع الحقول المطلوبة
- راجع قواعد التحقق في الـ Controllers

### خطأ 429 Too Many Requests
- انتظر دقيقة واحدة
- قلل من معدل الطلبات
- راجع إعدادات Rate Limiting
