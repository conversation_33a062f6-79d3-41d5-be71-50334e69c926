<?php

namespace App\Listeners;

use App\Events\SecurityEvent;
use App\Helpers\SecurityHelper;
use App\Models\AuditLog;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;

class SecurityEventListener
{
    /**
     * Handle the event.
     */
    public function handle(SecurityEvent $event): void
    {
        // Log the security event
        $this->logSecurityEvent($event);

        // Handle specific event types
        switch ($event->eventType) {
            case 'failed_login':
                $this->handleFailedLogin($event);
                break;
            case 'suspicious_activity':
                $this->handleSuspiciousActivity($event);
                break;
            case 'ip_blocked':
                $this->handleIPBlocked($event);
                break;
            case 'multiple_failed_attempts':
                $this->handleMultipleFailedAttempts($event);
                break;
            case 'unauthorized_access':
                $this->handleUnauthorizedAccess($event);
                break;
            case 'data_breach_attempt':
                $this->handleDataBreachAttempt($event);
                break;
        }
    }

    /**
     * Log security event to audit log
     */
    protected function logSecurityEvent(SecurityEvent $event): void
    {
        try {
            AuditLog::create([
                'user_id' => $event->userId,
                'action_type' => 'SECURITY_EVENT',
                'resource_type' => 'security',
                'description' => $this->getEventDescription($event->eventType),
                'ip_address' => $event->ipAddress,
                'user_agent' => $event->userAgent,
                'request_data' => json_encode($event->data),
                'success' => false,
                'created_at' => now()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create security audit log: ' . $e->getMessage());
        }
    }

    /**
     * Handle failed login attempts
     */
    protected function handleFailedLogin(SecurityEvent $event): void
    {
        $ip = $event->ipAddress;
        $attempts = SecurityHelper::incrementFailedAttempts($ip);

        // Block IP after 5 failed attempts
        if ($attempts >= 5) {
            SecurityHelper::blockIP($ip, 30); // Block for 30 minutes
            
            // Notify administrators
            $this->notifyAdministrators('IP blocked due to multiple failed login attempts', [
                'ip' => $ip,
                'attempts' => $attempts,
                'user_agent' => $event->userAgent
            ]);
        }
    }

    /**
     * Handle suspicious activity
     */
    protected function handleSuspiciousActivity(SecurityEvent $event): void
    {
        $ip = $event->ipAddress;
        
        // Temporarily block IP for suspicious activity
        SecurityHelper::blockIP($ip, 15); // Block for 15 minutes
        
        // Log detailed information
        Log::channel('security')->critical('Suspicious activity detected', [
            'ip' => $ip,
            'user_agent' => $event->userAgent,
            'activity_type' => $event->data['activity_type'] ?? 'unknown',
            'details' => $event->data
        ]);

        // Notify administrators immediately
        $this->notifyAdministrators('Suspicious activity detected', [
            'ip' => $ip,
            'activity' => $event->data['activity_type'] ?? 'unknown',
            'details' => $event->data
        ]);
    }

    /**
     * Handle IP blocking events
     */
    protected function handleIPBlocked(SecurityEvent $event): void
    {
        Log::channel('security')->warning('IP address blocked', [
            'ip' => $event->ipAddress,
            'reason' => $event->data['reason'] ?? 'security_violation',
            'duration' => $event->data['duration'] ?? 'unknown'
        ]);
    }

    /**
     * Handle multiple failed attempts
     */
    protected function handleMultipleFailedAttempts(SecurityEvent $event): void
    {
        $ip = $event->ipAddress;
        $attempts = $event->data['attempts'] ?? 0;

        if ($attempts >= 10) {
            // Block IP for longer duration
            SecurityHelper::blockIP($ip, 120); // Block for 2 hours
            
            // Send alert to administrators
            $this->notifyAdministrators('Critical: Multiple failed attempts detected', [
                'ip' => $ip,
                'attempts' => $attempts,
                'time_window' => '1 hour'
            ]);
        }
    }

    /**
     * Handle unauthorized access attempts
     */
    protected function handleUnauthorizedAccess(SecurityEvent $event): void
    {
        Log::channel('security')->alert('Unauthorized access attempt', [
            'ip' => $event->ipAddress,
            'user_id' => $event->userId,
            'resource' => $event->data['resource'] ?? 'unknown',
            'action' => $event->data['action'] ?? 'unknown'
        ]);

        // If user is involved, log them out from all sessions
        if ($event->userId) {
            $user = User::find($event->userId);
            if ($user) {
                // Invalidate all user sessions
                $user->tokens()->delete(); // For API tokens
                
                // Log the forced logout
                Log::channel('security')->info('User sessions invalidated due to unauthorized access', [
                    'user_id' => $event->userId,
                    'ip' => $event->ipAddress
                ]);
            }
        }
    }

    /**
     * Handle data breach attempts
     */
    protected function handleDataBreachAttempt(SecurityEvent $event): void
    {
        // This is critical - immediately block IP and notify
        SecurityHelper::blockIP($event->ipAddress, 1440); // Block for 24 hours
        
        Log::channel('security')->emergency('Data breach attempt detected', [
            'ip' => $event->ipAddress,
            'user_id' => $event->userId,
            'attempt_type' => $event->data['attempt_type'] ?? 'unknown',
            'details' => $event->data
        ]);

        // Send immediate notification to all administrators
        $this->notifyAdministrators('CRITICAL: Data breach attempt detected', [
            'ip' => $event->ipAddress,
            'user_id' => $event->userId,
            'attempt_type' => $event->data['attempt_type'] ?? 'unknown',
            'timestamp' => now()->toISOString()
        ], true);
    }

    /**
     * Get human-readable event description
     */
    protected function getEventDescription(string $eventType): string
    {
        $descriptions = [
            'failed_login' => 'محاولة تسجيل دخول فاشلة',
            'suspicious_activity' => 'نشاط مشبوه',
            'ip_blocked' => 'تم حظر عنوان IP',
            'multiple_failed_attempts' => 'محاولات فاشلة متعددة',
            'unauthorized_access' => 'محاولة وصول غير مصرح بها',
            'data_breach_attempt' => 'محاولة اختراق البيانات'
        ];

        return $descriptions[$eventType] ?? 'حدث أمني غير معروف';
    }

    /**
     * Notify administrators about security events
     */
    protected function notifyAdministrators(string $subject, array $data, bool $urgent = false): void
    {
        try {
            // Get admin users
            $admins = User::where('role', 'admin')->get();

            foreach ($admins as $admin) {
                // Send email notification
                if ($urgent) {
                    // For urgent notifications, send immediately
                    Mail::raw(
                        "تحذير أمني عاجل: {$subject}\n\nالتفاصيل:\n" . json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE),
                        function ($message) use ($admin, $subject) {
                            $message->to($admin->email)
                                   ->subject("[عاجل] {$subject}");
                        }
                    );
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to notify administrators: ' . $e->getMessage());
        }
    }
}
