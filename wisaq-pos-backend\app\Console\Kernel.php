<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Generate notifications every hour
        $schedule->command('notifications:generate --type=low-stock')
            ->hourly()
            ->withoutOverlapping()
            ->runInBackground();

        // Generate expiry notifications daily at 9 AM
        $schedule->command('notifications:generate --type=expiry')
            ->dailyAt('09:00')
            ->withoutOverlapping()
            ->runInBackground();

        // Generate subscription notifications daily at 10 AM
        $schedule->command('notifications:generate --type=subscription')
            ->dailyAt('10:00')
            ->withoutOverlapping()
            ->runInBackground();

        // Clean old notifications (older than 30 days) weekly
        $schedule->call(function () {
            \App\Models\Notification::where('created_at', '<', now()->subDays(30))->delete();
        })->weekly();

        // Clean old activity logs (older than 90 days) weekly
        $schedule->call(function () {
            \Spatie\Activitylog\Models\Activity::where('created_at', '<', now()->subDays(90))->delete();
        })->weekly();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
