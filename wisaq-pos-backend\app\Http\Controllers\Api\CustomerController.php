<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class CustomerController extends Controller
{
    /**
     * عرض قائمة العملاء
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $perPage = $request->get('per_page', 15);

            $query = Customer::where('company_id', $companyId);

            // فلترة حسب النوع
            if ($request->has('type') && $request->type) {
                $query->where('type', $request->type);
            }

            // فلترة حسب الحالة
            if ($request->has('status')) {
                if ($request->status === 'active') {
                    $query->where('is_active', true);
                } elseif ($request->status === 'inactive') {
                    $query->where('is_active', false);
                }
            }

            // البحث
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%")
                      ->orWhere('tax_number', 'like', "%{$search}%");
                });
            }

            // الترتيب
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            if ($request->has('paginate') && $request->paginate === 'false') {
                $customers = $query->get();
                return response()->json([
                    'success' => true,
                    'data' => $customers
                ]);
            }

            $customers = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => [
                    'customers' => $customers->items(),
                    'pagination' => [
                        'current_page' => $customers->currentPage(),
                        'last_page' => $customers->lastPage(),
                        'per_page' => $customers->perPage(),
                        'total' => $customers->total(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب العملاء'
            ], 500);
        }
    }

    /**
     * إنشاء عميل جديد
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'nullable|email|unique:customers,email',
                'phone' => 'nullable|string|max:20',
                'address' => 'nullable|string',
                'city' => 'nullable|string|max:100',
                'tax_number' => 'nullable|string|max:50',
                'type' => 'required|in:individual,company',
                'credit_limit' => 'nullable|numeric|min:0',
                'birth_date' => 'nullable|date|before:today',
                'gender' => 'nullable|in:male,female',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $companyId = Auth::user()->company_id;

            $data = $request->all();
            $data['company_id'] = $companyId;
            $data['current_balance'] = 0;
            $data['is_active'] = true;

            $customer = Customer::create($data);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء العميل بنجاح',
                'data' => $customer
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء العميل'
            ], 500);
        }
    }

    /**
     * عرض عميل محدد
     */
    public function show($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $customer = Customer::where('company_id', $companyId)->find($id);

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'العميل غير موجود'
                ], 404);
            }

            // إضافة إحصائيات العميل
            $stats = [
                'total_purchases' => $customer->sales()->completed()->count(),
                'total_spent' => $customer->sales()->completed()->sum('total_amount'),
                'last_purchase' => $customer->sales()->completed()->latest()->first()?->sale_date,
                'average_purchase' => $customer->sales()->completed()->avg('total_amount') ?? 0,
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'customer' => $customer,
                    'stats' => $stats
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب بيانات العميل'
            ], 500);
        }
    }

    /**
     * تحديث عميل
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $customer = Customer::where('company_id', $companyId)->find($id);

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'العميل غير موجود'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'nullable|email|unique:customers,email,' . $id,
                'phone' => 'nullable|string|max:20',
                'address' => 'nullable|string',
                'city' => 'nullable|string|max:100',
                'tax_number' => 'nullable|string|max:50',
                'type' => 'required|in:individual,company',
                'credit_limit' => 'nullable|numeric|min:0',
                'birth_date' => 'nullable|date|before:today',
                'gender' => 'nullable|in:male,female',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $customer->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث العميل بنجاح',
                'data' => $customer
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث العميل'
            ], 500);
        }
    }

    /**
     * حذف عميل
     */
    public function destroy($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $customer = Customer::where('company_id', $companyId)->find($id);

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'العميل غير موجود'
                ], 404);
            }

            // التحقق من وجود مبيعات للعميل
            if ($customer->sales()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف العميل لوجود مبيعات مرتبطة به'
                ], 400);
            }

            $customer->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف العميل بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في حذف العميل'
            ], 500);
        }
    }

    /**
     * تبديل حالة العميل
     */
    public function toggleStatus($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $customer = Customer::where('company_id', $companyId)->find($id);

            if (!$customer) {
                return response()->json([
                    'success' => false,
                    'message' => 'العميل غير موجود'
                ], 404);
            }

            $customer->update(['is_active' => !$customer->is_active]);

            return response()->json([
                'success' => true,
                'message' => $customer->is_active ? 'تم تفعيل العميل' : 'تم إلغاء تفعيل العميل',
                'data' => $customer
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تغيير حالة العميل'
            ], 500);
        }
    }

    /**
     * البحث في العملاء
     */
    public function search($query): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $customers = Customer::where('company_id', $companyId)
                ->where('is_active', true)
                ->where(function($q) use ($query) {
                    $q->where('name', 'like', "%{$query}%")
                      ->orWhere('phone', 'like', "%{$query}%")
                      ->orWhere('email', 'like', "%{$query}%");
                })
                ->limit(20)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $customers
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في البحث'
            ], 500);
        }
    }

    /**
     * تقرير العملاء
     */
    public function customerReport(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $customers = Customer::where('company_id', $companyId)
                ->withCount(['sales as total_orders'])
                ->withSum(['sales as total_spent' => function($query) {
                    $query->where('status', 'completed');
                }], 'total_amount')
                ->get();

            $totalCustomers = $customers->count();
            $activeCustomers = $customers->where('is_active', true)->count();
            $totalRevenue = $customers->sum('total_spent');
            $averageOrderValue = $customers->where('total_orders', '>', 0)->avg('total_spent');

            return response()->json([
                'success' => true,
                'data' => [
                    'summary' => [
                        'total_customers' => $totalCustomers,
                        'active_customers' => $activeCustomers,
                        'total_revenue' => $totalRevenue,
                        'average_order_value' => $averageOrderValue,
                    ],
                    'customers' => $customers
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء تقرير العملاء'
            ], 500);
        }
    }
}
