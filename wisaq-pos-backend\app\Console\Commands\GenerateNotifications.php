<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;
use App\Models\Company;
use App\Models\Notification;
use Carbon\Carbon;

class GenerateNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notifications:generate {--type=all : Type of notifications to generate (all, low-stock, expiry, subscription)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate automatic notifications for low stock, expiry dates, and subscription warnings';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->option('type');
        
        $this->info('Starting notification generation...');
        
        switch ($type) {
            case 'low-stock':
                $this->generateLowStockNotifications();
                break;
            case 'expiry':
                $this->generateExpiryNotifications();
                break;
            case 'subscription':
                $this->generateSubscriptionNotifications();
                break;
            case 'all':
            default:
                $this->generateLowStockNotifications();
                $this->generateExpiryNotifications();
                $this->generateSubscriptionNotifications();
                break;
        }
        
        $this->info('Notification generation completed!');
    }

    /**
     * Generate low stock notifications
     */
    private function generateLowStockNotifications()
    {
        $this->info('Generating low stock notifications...');
        
        $companies = Company::where('status', 'active')->get();
        $totalNotifications = 0;
        
        foreach ($companies as $company) {
            $lowStockProducts = Product::where('company_id', $company->id)
                ->where('track_quantity', true)
                ->where('is_active', true)
                ->whereColumn('quantity', '<=', 'min_quantity')
                ->get();

            foreach ($lowStockProducts as $product) {
                // Check if notification already exists in last 24 hours
                $existingNotification = Notification::where('company_id', $company->id)
                    ->where('type', 'low_stock')
                    ->where('data->product_id', $product->id)
                    ->where('created_at', '>=', now()->subDay())
                    ->first();

                if (!$existingNotification) {
                    Notification::create([
                        'company_id' => $company->id,
                        'type' => 'low_stock',
                        'title' => 'مخزون منخفض',
                        'message' => "المنتج '{$product->name}' وصل إلى الحد الأدنى للمخزون",
                        'data' => [
                            'product_id' => $product->id,
                            'product_name' => $product->name,
                            'current_quantity' => $product->quantity,
                            'min_quantity' => $product->min_quantity,
                        ],
                        'priority' => 'medium'
                    ]);
                    $totalNotifications++;
                }
            }
        }
        
        $this->info("Generated {$totalNotifications} low stock notifications");
    }

    /**
     * Generate expiry notifications
     */
    private function generateExpiryNotifications()
    {
        $this->info('Generating expiry notifications...');
        
        $companies = Company::where('status', 'active')->get();
        $totalNotifications = 0;
        
        foreach ($companies as $company) {
            // Products expiring in 30, 7, 3, 1 days
            $expiringProducts = Product::where('company_id', $company->id)
                ->where('is_active', true)
                ->whereNotNull('expiry_date')
                ->where('expiry_date', '<=', now()->addDays(30))
                ->where('expiry_date', '>', now())
                ->get();

            foreach ($expiringProducts as $product) {
                $daysUntilExpiry = now()->diffInDays($product->expiry_date);
                
                // Create notifications at specific intervals
                $notificationDays = [30, 7, 3, 1];
                
                if (in_array($daysUntilExpiry, $notificationDays)) {
                    // Check if notification already exists
                    $existingNotification = Notification::where('company_id', $company->id)
                        ->where('type', 'expiry_warning')
                        ->where('data->product_id', $product->id)
                        ->where('data->days_until_expiry', $daysUntilExpiry)
                        ->first();

                    if (!$existingNotification) {
                        $priority = $daysUntilExpiry <= 3 ? 'high' : 'medium';
                        
                        Notification::create([
                            'company_id' => $company->id,
                            'type' => 'expiry_warning',
                            'title' => 'تحذير انتهاء صلاحية',
                            'message' => "المنتج '{$product->name}' سينتهي خلال {$daysUntilExpiry} يوم",
                            'data' => [
                                'product_id' => $product->id,
                                'product_name' => $product->name,
                                'expiry_date' => $product->expiry_date,
                                'days_until_expiry' => $daysUntilExpiry,
                            ],
                            'priority' => $priority
                        ]);
                        $totalNotifications++;
                    }
                }
            }
        }
        
        $this->info("Generated {$totalNotifications} expiry notifications");
    }

    /**
     * Generate subscription notifications
     */
    private function generateSubscriptionNotifications()
    {
        $this->info('Generating subscription notifications...');
        
        $companies = Company::with('subscription')
            ->where('status', 'active')
            ->whereHas('subscription')
            ->get();
        
        $totalNotifications = 0;
        
        foreach ($companies as $company) {
            $subscription = $company->subscription;
            $daysUntilExpiry = now()->diffInDays($subscription->expires_at, false);
            
            // Subscription expiry warnings (30, 7, 3, 1 days)
            $notificationDays = [30, 7, 3, 1];
            
            if ($daysUntilExpiry >= 0 && in_array($daysUntilExpiry, $notificationDays)) {
                // Check if notification already exists today
                $existingNotification = Notification::where('company_id', $company->id)
                    ->where('type', 'subscription_expiry')
                    ->where('data->days_until_expiry', $daysUntilExpiry)
                    ->whereDate('created_at', today())
                    ->first();

                if (!$existingNotification) {
                    $priority = $daysUntilExpiry <= 3 ? 'high' : 'medium';
                    
                    Notification::create([
                        'company_id' => $company->id,
                        'type' => 'subscription_expiry',
                        'title' => 'تحذير انتهاء الاشتراك',
                        'message' => "اشتراكك سينتهي خلال {$daysUntilExpiry} يوم. يرجى التجديد لتجنب انقطاع الخدمة",
                        'data' => [
                            'subscription_id' => $subscription->id,
                            'expires_at' => $subscription->expires_at,
                            'days_until_expiry' => $daysUntilExpiry,
                            'plan_name' => $subscription->plan->name ?? 'الخطة الأساسية',
                        ],
                        'priority' => $priority
                    ]);
                    $totalNotifications++;
                }
            }

            // Subscription expired notification
            if ($daysUntilExpiry < 0) {
                $existingNotification = Notification::where('company_id', $company->id)
                    ->where('type', 'subscription_expired')
                    ->whereDate('created_at', today())
                    ->first();

                if (!$existingNotification) {
                    Notification::create([
                        'company_id' => $company->id,
                        'type' => 'subscription_expired',
                        'title' => 'انتهى الاشتراك',
                        'message' => 'انتهى اشتراكك. يرجى التجديد فوراً لاستمرار الخدمة',
                        'data' => [
                            'subscription_id' => $subscription->id,
                            'expired_at' => $subscription->expires_at,
                            'days_since_expiry' => abs($daysUntilExpiry),
                        ],
                        'priority' => 'high'
                    ]);
                    $totalNotifications++;
                    
                    // Suspend company if expired for more than 7 days
                    if (abs($daysUntilExpiry) > 7) {
                        $company->update(['status' => 'suspended']);
                        $this->warn("Company {$company->name} suspended due to expired subscription");
                    }
                }
            }
        }
        
        $this->info("Generated {$totalNotifications} subscription notifications");
    }
}
