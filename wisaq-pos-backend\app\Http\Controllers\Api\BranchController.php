<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Branch;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

class BranchController extends Controller
{
    /**
     * عرض قائمة الفروع
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $perPage = $request->get('per_page', 15);

            $query = Branch::with(['users'])
                ->where('company_id', $companyId);

            // فلترة حسب الحالة
            if ($request->has('status') && $request->status) {
                if ($request->status === 'active') {
                    $query->where('status', 'active');
                } elseif ($request->status === 'inactive') {
                    $query->where('status', 'inactive');
                }
            }

            // البحث
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('name_en', 'like', "%{$search}%")
                      ->orWhere('code', 'like', "%{$search}%")
                      ->orWhere('address', 'like', "%{$search}%");
                });
            }

            // الترتيب
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            if ($request->has('paginate') && $request->paginate === 'false') {
                $branches = $query->get();
                return response()->json([
                    'success' => true,
                    'data' => $branches
                ]);
            }

            $branches = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => [
                    'branches' => $branches->items(),
                    'pagination' => [
                        'current_page' => $branches->currentPage(),
                        'last_page' => $branches->lastPage(),
                        'per_page' => $branches->perPage(),
                        'total' => $branches->total(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب الفروع'
            ], 500);
        }
    }

    /**
     * إنشاء فرع جديد
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'name_en' => 'nullable|string|max:255',
                'phone' => 'nullable|string|max:20',
                'address' => 'required|string',
                'address_en' => 'nullable|string',
                'city' => 'required|string|max:100',
                'latitude' => 'nullable|numeric|between:-90,90',
                'longitude' => 'nullable|numeric|between:-180,180',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $companyId = Auth::user()->company_id;
            $company = Company::find($companyId);

            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'الشركة غير موجودة'
                ], 404);
            }

            // التحقق من حد الفروع المسموح
            $currentBranches = $company->branches()->count();
            if ($currentBranches >= $company->max_branches) {
                return response()->json([
                    'success' => false,
                    'message' => 'تم الوصول للحد الأقصى من الفروع المسموح. يرجى ترقية الاشتراك.'
                ], 400);
            }

            DB::beginTransaction();

            try {
                $data = $request->all();
                $data['company_id'] = $companyId;
                $data['status'] = 'active';
                $data['is_main'] = false;

                // إنشاء كود الفرع
                $branchCount = $company->branches()->count();
                $data['code'] = 'BR-' . str_pad($branchCount + 1, 3, '0', STR_PAD_LEFT);

                $branch = Branch::create($data);

                // تحديث عدد الفروع في الشركة
                $company->increment('current_branches');

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'تم إنشاء الفرع بنجاح',
                    'data' => $branch->load(['users'])
                ], 201);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء الفرع'
            ], 500);
        }
    }

    /**
     * عرض فرع محدد
     */
    public function show($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $branch = Branch::with(['users', 'company'])
                ->where('company_id', $companyId)
                ->find($id);

            if (!$branch) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفرع غير موجود'
                ], 404);
            }

            // إضافة إحصائيات الفرع
            $stats = [
                'total_users' => $branch->users()->count(),
                'active_users' => $branch->users()->where('status', 'active')->count(),
                'total_sales_today' => $branch->sales()->whereDate('sale_date', today())->sum('total_amount'),
                'total_sales_month' => $branch->sales()->whereMonth('sale_date', now()->month)->sum('total_amount'),
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'branch' => $branch,
                    'stats' => $stats
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب بيانات الفرع'
            ], 500);
        }
    }

    /**
     * تحديث فرع
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $branch = Branch::where('company_id', $companyId)->find($id);

            if (!$branch) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفرع غير موجود'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'name_en' => 'nullable|string|max:255',
                'phone' => 'nullable|string|max:20',
                'address' => 'required|string',
                'address_en' => 'nullable|string',
                'city' => 'required|string|max:100',
                'latitude' => 'nullable|numeric|between:-90,90',
                'longitude' => 'nullable|numeric|between:-180,180',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $branch->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث الفرع بنجاح',
                'data' => $branch->load(['users'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث الفرع'
            ], 500);
        }
    }

    /**
     * حذف فرع
     */
    public function destroy($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $branch = Branch::where('company_id', $companyId)->find($id);

            if (!$branch) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفرع غير موجود'
                ], 404);
            }

            // منع حذف الفرع الرئيسي
            if ($branch->is_main) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف الفرع الرئيسي'
                ], 400);
            }

            // التحقق من وجود مستخدمين أو مبيعات في الفرع
            if ($branch->users()->exists() || $branch->sales()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف الفرع لوجود مستخدمين أو مبيعات مرتبطة به'
                ], 400);
            }

            DB::beginTransaction();

            try {
                $branch->delete();

                // تحديث عدد الفروع في الشركة
                $company = Company::find($companyId);
                $company->decrement('current_branches');

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'تم حذف الفرع بنجاح'
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في حذف الفرع'
            ], 500);
        }
    }

    /**
     * تعيين فرع كرئيسي
     */
    public function setAsMain($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $branch = Branch::where('company_id', $companyId)->find($id);

            if (!$branch) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفرع غير موجود'
                ], 404);
            }

            DB::beginTransaction();

            try {
                // إزالة الفرع الرئيسي الحالي
                Branch::where('company_id', $companyId)
                    ->where('is_main', true)
                    ->update(['is_main' => false]);

                // تعيين الفرع الجديد كرئيسي
                $branch->update(['is_main' => true]);

                DB::commit();

                return response()->json([
                    'success' => true,
                    'message' => 'تم تعيين الفرع كرئيسي بنجاح',
                    'data' => $branch
                ]);

            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تعيين الفرع الرئيسي'
            ], 500);
        }
    }
}
