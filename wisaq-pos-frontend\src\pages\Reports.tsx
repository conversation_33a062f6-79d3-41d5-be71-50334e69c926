import React, { useState } from 'react';
import {
  ChartBarIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  ShoppingCartIcon,
  CubeIcon,
  UsersIcon,
  CalendarDaysIcon,
  ArrowDownTrayIcon,
} from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';
import { useDashboardStats, useSalesReport, useInventoryReport } from '../hooks/useApi';

const Reports: React.FC = () => {
  const { isRTL } = useTheme();
  const [selectedPeriod, setSelectedPeriod] = useState('today');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [reportType, setReportType] = useState('sales');

  const { data: dashboardStats, loading: statsLoading } = useDashboardStats({ period: selectedPeriod });
  const { data: salesReport, loading: salesLoading } = useSalesReport({
    date_from: dateFrom,
    date_to: dateTo,
    group_by: 'day'
  });
  const { data: inventoryReport, loading: inventoryLoading } = useInventoryReport();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(isRTL ? 'ar-SA' : 'en-US', {
      style: 'currency',
      currency: 'SAR',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const exportReport = (type: string) => {
    // Implementation for exporting reports
    console.log(`Exporting ${type} report`);
  };

  if (statsLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-48 animate-pulse"></div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-32 animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="card p-6 animate-pulse">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {isRTL ? 'التقارير والإحصائيات' : 'Reports & Analytics'}
          </h1>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {isRTL ? 'تقارير مفصلة عن أداء متجرك' : 'Detailed reports on your store performance'}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="input-field"
          >
            <option value="today">{isRTL ? 'اليوم' : 'Today'}</option>
            <option value="week">{isRTL ? 'هذا الأسبوع' : 'This Week'}</option>
            <option value="month">{isRTL ? 'هذا الشهر' : 'This Month'}</option>
            <option value="year">{isRTL ? 'هذا العام' : 'This Year'}</option>
          </select>
          <button
            onClick={() => exportReport('dashboard')}
            className="btn-secondary flex items-center gap-2"
          >
            <ArrowDownTrayIcon className="h-5 w-5" />
            {isRTL ? 'تصدير' : 'Export'}
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="bg-green-500 p-3 rounded-lg">
                <CurrencyDollarIcon className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'} flex-1`}>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {isRTL ? 'إجمالي المبيعات' : 'Total Revenue'}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(dashboardStats?.sales_stats?.total_revenue || 0)}
              </p>
              <p className="text-sm text-green-600 dark:text-green-400">
                +12% {isRTL ? 'من الفترة السابقة' : 'from last period'}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="bg-blue-500 p-3 rounded-lg">
                <ShoppingCartIcon className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'} flex-1`}>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {isRTL ? 'عدد المبيعات' : 'Total Sales'}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardStats?.sales_stats?.total_sales || 0}
              </p>
              <p className="text-sm text-blue-600 dark:text-blue-400">
                {formatCurrency(dashboardStats?.sales_stats?.average_order_value || 0)} {isRTL ? 'متوسط الطلب' : 'avg order'}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="bg-purple-500 p-3 rounded-lg">
                <CubeIcon className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'} flex-1`}>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {isRTL ? 'مخزون منخفض' : 'Low Stock Items'}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardStats?.inventory_stats?.low_stock_products || 0}
              </p>
              <p className="text-sm text-orange-600 dark:text-orange-400">
                {dashboardStats?.inventory_stats?.out_of_stock_products || 0} {isRTL ? 'نفد المخزون' : 'out of stock'}
              </p>
            </div>
          </div>
        </div>

        <div className="card p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="bg-orange-500 p-3 rounded-lg">
                <UsersIcon className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className={`${isRTL ? 'mr-4' : 'ml-4'} flex-1`}>
              <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                {isRTL ? 'إجمالي العملاء' : 'Total Customers'}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {dashboardStats?.customer_stats?.total_customers || 0}
              </p>
              <p className="text-sm text-green-600 dark:text-green-400">
                +{dashboardStats?.customer_stats?.new_customers_this_month || 0} {isRTL ? 'هذا الشهر' : 'this month'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Report Tabs */}
      <div className="card">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="-mb-px flex space-x-8" aria-label="Tabs">
            <button
              onClick={() => setReportType('sales')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                reportType === 'sales'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <div className="flex items-center gap-2">
                <ChartBarIcon className="h-5 w-5" />
                {isRTL ? 'تقرير المبيعات' : 'Sales Report'}
              </div>
            </button>
            <button
              onClick={() => setReportType('inventory')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                reportType === 'inventory'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <div className="flex items-center gap-2">
                <CubeIcon className="h-5 w-5" />
                {isRTL ? 'تقرير المخزون' : 'Inventory Report'}
              </div>
            </button>
            <button
              onClick={() => setReportType('customers')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                reportType === 'customers'
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <div className="flex items-center gap-2">
                <UsersIcon className="h-5 w-5" />
                {isRTL ? 'تقرير العملاء' : 'Customer Report'}
              </div>
            </button>
          </nav>
        </div>

        <div className="p-6">
          {reportType === 'sales' && (
            <div className="space-y-6">
              {/* Sales Chart */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  {isRTL ? 'مبيعات آخر 7 أيام' : 'Sales Last 7 Days'}
                </h3>
                <div className="h-64 bg-gray-50 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                  <p className="text-gray-500 dark:text-gray-400">
                    {isRTL ? 'الرسم البياني سيتم إضافته قريباً' : 'Chart will be added soon'}
                  </p>
                </div>
              </div>

              {/* Top Products */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  {isRTL ? 'أفضل المنتجات مبيعاً' : 'Top Selling Products'}
                </h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="table-header">{isRTL ? 'المنتج' : 'Product'}</th>
                        <th className="table-header">{isRTL ? 'الكمية المباعة' : 'Quantity Sold'}</th>
                        <th className="table-header">{isRTL ? 'إجمالي المبيعات' : 'Total Revenue'}</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                      {dashboardStats?.top_selling_products?.map((product: any, index: number) => (
                        <tr key={index}>
                          <td className="table-cell font-medium">{product.product_name}</td>
                          <td className="table-cell">{product.total_quantity}</td>
                          <td className="table-cell">{formatCurrency(product.total_revenue)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {reportType === 'inventory' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="card p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    {isRTL ? 'إجمالي قيمة المخزون' : 'Total Inventory Value'}
                  </h4>
                  <p className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                    {formatCurrency(inventoryReport?.summary?.total_inventory_value || 0)}
                  </p>
                </div>
                <div className="card p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    {isRTL ? 'المنتجات النشطة' : 'Active Products'}
                  </h4>
                  <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {inventoryReport?.summary?.active_products || 0}
                  </p>
                </div>
                <div className="card p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                    {isRTL ? 'الربح المتوقع' : 'Potential Profit'}
                  </h4>
                  <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                    {formatCurrency(inventoryReport?.summary?.potential_profit || 0)}
                  </p>
                </div>
              </div>

              {/* Low Stock Products */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  {isRTL ? 'منتجات منخفضة المخزون' : 'Low Stock Products'}
                </h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="table-header">{isRTL ? 'المنتج' : 'Product'}</th>
                        <th className="table-header">{isRTL ? 'الكمية الحالية' : 'Current Stock'}</th>
                        <th className="table-header">{isRTL ? 'الحد الأدنى' : 'Min Stock'}</th>
                        <th className="table-header">{isRTL ? 'الحالة' : 'Status'}</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                      {inventoryReport?.low_stock_products?.map((product: any, index: number) => (
                        <tr key={index}>
                          <td className="table-cell font-medium">{product.name}</td>
                          <td className="table-cell">{product.quantity}</td>
                          <td className="table-cell">{product.min_quantity}</td>
                          <td className="table-cell">
                            <span className="badge badge-warning">
                              {isRTL ? 'مخزون منخفض' : 'Low Stock'}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {reportType === 'customers' && (
            <div className="space-y-6">
              <div className="text-center py-12">
                <UsersIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                  {isRTL ? 'تقرير العملاء' : 'Customer Report'}
                </h3>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  {isRTL ? 'سيتم إضافة تقرير العملاء قريباً' : 'Customer report will be added soon'}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Reports;
