<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('company_id')->constrained()->onDelete('cascade');
            $table->foreignId('subscription_id')->constrained()->onDelete('cascade');
            $table->string('payment_id')->unique(); // معرف الدفع من Tap
            $table->string('charge_id')->nullable(); // معرف الشحن من Tap
            $table->decimal('amount', 10, 2); // المبلغ
            $table->string('currency', 3)->default('SAR'); // العملة
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled', 'refunded'])->default('pending');
            $table->enum('payment_method', ['card', 'apple_pay', 'google_pay', 'stc_pay', 'bank_transfer'])->default('card');
            $table->json('payment_details')->nullable(); // تفاصيل الدفع من Tap
            $table->json('tap_response')->nullable(); // استجابة Tap كاملة
            $table->string('receipt_url')->nullable(); // رابط الإيصال
            $table->string('failure_reason')->nullable(); // سبب الفشل
            $table->timestamp('paid_at')->nullable(); // تاريخ الدفع
            $table->timestamp('failed_at')->nullable(); // تاريخ الفشل
            $table->timestamps();

            // Indexes
            $table->index(['company_id', 'status']);
            $table->index(['subscription_id', 'status']);
            $table->index(['payment_id', 'charge_id']);
            $table->index(['status', 'paid_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
