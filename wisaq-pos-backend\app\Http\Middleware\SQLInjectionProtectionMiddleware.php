<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class SQLInjectionProtectionMiddleware
{
    /**
     * SQL injection patterns to detect
     */
    protected $sqlPatterns = [
        '/(\s|^)(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript|onload|onerror|onclick)(\s|$)/i',
        '/(\s|^)(or|and)(\s+)(\d+)(\s*)=(\s*)(\d+)/i',
        '/(\s|^)(or|and)(\s+)(\'|\")(\w+)(\'|\")(\s*)=(\s*)(\'|\")(\w+)(\'|\")/i',
        '/(\s|^)(or|and)(\s+)(\w+)(\s*)=(\s*)(\w+)/i',
        '/(\'|\")(\s*)(or|and)(\s*)(\'|\")/i',
        '/(\s|^)(union)(\s+)(all|distinct)?(\s+)(select)/i',
        '/(\s|^)(select)(\s+)(.+)(\s+)(from)/i',
        '/(\s|^)(insert)(\s+)(into)/i',
        '/(\s|^)(update)(\s+)(.+)(\s+)(set)/i',
        '/(\s|^)(delete)(\s+)(from)/i',
        '/(\s|^)(drop|create|alter)(\s+)(table|database|index)/i',
        '/(\s|^)(exec|execute)(\s*)\(/i',
        '/(\s|^)(sp_|xp_)/i',
        '/(\s|^)(waitfor|delay)(\s+)/i',
        '/(\s|^)(benchmark|sleep)(\s*)\(/i',
        '/(\s|^)(load_file|into\s+outfile|into\s+dumpfile)/i',
        '/(\s|^)(information_schema|mysql|performance_schema|sys)/i',
        '/(\s|^)(concat|group_concat|char|ascii|substring|mid|left|right)/i',
        '/(\s|^)(version|user|database|schema)/i',
        '/(\s|^)(having|group\s+by|order\s+by|limit)/i',
        '/(\-\-|\#|\/\*|\*\/)/i',
        '/(;|\||&)/i'
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check all input for SQL injection patterns
        $input = $request->all();
        
        if ($this->containsSQLInjection($input)) {
            Log::warning('SQL Injection attempt detected', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'input' => $input,
                'user_id' => $request->user() ? $request->user()->id : null,
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'طلب غير صالح تم رفضه لأسباب أمنية',
                'error_code' => 'SECURITY_VIOLATION'
            ], 400);
        }

        return $next($request);
    }

    /**
     * Check if input contains SQL injection patterns
     */
    protected function containsSQLInjection($input): bool
    {
        if (is_array($input)) {
            foreach ($input as $value) {
                if ($this->containsSQLInjection($value)) {
                    return true;
                }
            }
            return false;
        }

        if (is_string($input)) {
            foreach ($this->sqlPatterns as $pattern) {
                if (preg_match($pattern, $input)) {
                    return true;
                }
            }
        }

        return false;
    }
}
