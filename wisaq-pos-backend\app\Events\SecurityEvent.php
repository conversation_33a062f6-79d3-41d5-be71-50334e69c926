<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SecurityEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public string $eventType;
    public array $data;
    public ?int $userId;
    public string $ipAddress;
    public string $userAgent;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $eventType,
        array $data = [],
        ?int $userId = null,
        ?string $ipAddress = null,
        ?string $userAgent = null
    ) {
        $this->eventType = $eventType;
        $this->data = $data;
        $this->userId = $userId;
        $this->ipAddress = $ipAddress ?? request()->ip();
        $this->userAgent = $userAgent ?? request()->userAgent();
    }
}
