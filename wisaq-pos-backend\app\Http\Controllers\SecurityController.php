<?php

namespace App\Http\Controllers;

use App\Events\SecurityEvent;
use App\Helpers\SecurityHelper;
use App\Http\Requests\SecurePasswordRequest;
use App\Models\AuditLog;
use App\Models\User;
use App\Services\SecurityService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class SecurityController extends Controller
{
    protected SecurityService $securityService;

    public function __construct(SecurityService $securityService)
    {
        $this->securityService = $securityService;
    }

    /**
     * Change user password
     */
    public function changePassword(SecurePasswordRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Verify current password
            if (!Hash::check($request->current_password, $user->password)) {
                // Log failed password change attempt
                SecurityHelper::logSecurityEvent('Failed password change attempt', [
                    'user_id' => $user->id,
                    'reason' => 'incorrect_current_password'
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'كلمة المرور الحالية غير صحيحة'
                ], 400);
            }

            // Update password
            $user->update([
                'password' => $this->securityService->hashPassword($request->password),
                'password_changed_at' => now(),
            ]);

            // Invalidate all other sessions/tokens
            $user->tokens()->delete();

            // Log successful password change
            SecurityHelper::logSecurityEvent('Password changed successfully', [
                'user_id' => $user->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم تغيير كلمة المرور بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تغيير كلمة المرور'
            ], 500);
        }
    }

    /**
     * Get security dashboard data
     */
    public function securityDashboard(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Get recent security events for user
            $recentEvents = AuditLog::where('user_id', $user->id)
                ->where('created_at', '>=', now()->subDays(30))
                ->orderBy('created_at', 'desc')
                ->limit(10)
                ->get();

            // Get failed login attempts
            $failedAttempts = SecurityHelper::getFailedAttempts(request()->ip());

            // Check if IP is blocked
            $isBlocked = SecurityHelper::isIPBlocked(request()->ip());

            // Get active sessions count (approximate)
            $activeSessions = $user->tokens()->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'recent_events' => $recentEvents,
                    'failed_attempts' => $failedAttempts,
                    'is_blocked' => $isBlocked,
                    'active_sessions' => $activeSessions,
                    'last_login' => $user->last_login_at,
                    'password_changed_at' => $user->password_changed_at,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب بيانات الأمان'
            ], 500);
        }
    }

    /**
     * Validate password strength
     */
    public function validatePassword(Request $request): JsonResponse
    {
        $request->validate([
            'password' => 'required|string'
        ]);

        $validation = $this->securityService->validatePasswordStrength($request->password);

        return response()->json([
            'success' => true,
            'data' => $validation
        ]);
    }

    /**
     * Generate secure token
     */
    public function generateToken(Request $request): JsonResponse
    {
        $request->validate([
            'length' => 'sometimes|integer|min:8|max:128'
        ]);

        $length = $request->input('length', 32);
        $token = $this->securityService->generateSecureToken($length);

        return response()->json([
            'success' => true,
            'data' => [
                'token' => $token,
                'length' => strlen($token)
            ]
        ]);
    }

    /**
     * Get audit logs for current user
     */
    public function auditLogs(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $perPage = $request->input('per_page', 15);

            $logs = AuditLog::where('user_id', $user->id)
                ->when($request->action_type, function ($query, $actionType) {
                    return $query->where('action_type', $actionType);
                })
                ->when($request->date_from, function ($query, $dateFrom) {
                    return $query->where('created_at', '>=', $dateFrom);
                })
                ->when($request->date_to, function ($query, $dateTo) {
                    return $query->where('created_at', '<=', $dateTo);
                })
                ->orderBy('created_at', 'desc')
                ->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $logs
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في جلب سجلات المراجعة'
            ], 500);
        }
    }

    /**
     * Revoke all sessions/tokens
     */
    public function revokeAllSessions(): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Delete all tokens except current one
            $currentToken = $user->currentAccessToken();
            $user->tokens()->where('id', '!=', $currentToken->id)->delete();

            // Log the action
            SecurityHelper::logSecurityEvent('All sessions revoked', [
                'user_id' => $user->id
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء جميع الجلسات بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في إلغاء الجلسات'
            ], 500);
        }
    }

    /**
     * Report security incident
     */
    public function reportIncident(Request $request): JsonResponse
    {
        $request->validate([
            'incident_type' => 'required|string|in:suspicious_activity,unauthorized_access,data_breach,other',
            'description' => 'required|string|max:1000',
            'severity' => 'required|string|in:low,medium,high,critical'
        ]);

        try {
            // Create audit log entry
            AuditLog::create([
                'user_id' => Auth::id(),
                'action_type' => 'SECURITY_INCIDENT_REPORT',
                'resource_type' => 'security',
                'description' => 'تقرير حادث أمني: ' . $request->incident_type,
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'request_data' => json_encode($request->only(['incident_type', 'description', 'severity'])),
                'success' => true,
                'created_at' => now()
            ]);

            // Fire security event
            event(new SecurityEvent(
                'security_incident_reported',
                [
                    'incident_type' => $request->incident_type,
                    'description' => $request->description,
                    'severity' => $request->severity
                ],
                Auth::id()
            ));

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال تقرير الحادث الأمني بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'فشل في إرسال تقرير الحادث'
            ], 500);
        }
    }

    /**
     * Get security settings
     */
    public function getSecuritySettings(): JsonResponse
    {
        $settings = [
            'password_requirements' => config('security.password'),
            'session_timeout' => config('security.session.session_timeout'),
            'rate_limits' => config('security.rate_limits'),
            'two_factor_enabled' => false, // Will be implemented later
        ];

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }
}
