import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse, AuthResponse, LoginCredentials } from '../types';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = this.getRefreshToken();
            if (refreshToken) {
              const response = await this.refreshToken();
              this.setToken(response.data.token);
              return this.api(originalRequest);
            }
          } catch (refreshError) {
            this.logout();
            window.location.href = '/login';
          }
        }

        return Promise.reject(error);
      }
    );
  }

  // Token management
  private getToken(): string | null {
    return localStorage.getItem('wisaq_token');
  }

  private getRefreshToken(): string | null {
    return localStorage.getItem('wisaq_refresh_token');
  }

  private setToken(token: string): void {
    localStorage.setItem('wisaq_token', token);
  }

  private removeToken(): void {
    localStorage.removeItem('wisaq_token');
    localStorage.removeItem('wisaq_refresh_token');
    localStorage.removeItem('wisaq_user');
  }

  // Auth methods
  async login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> {
    const response = await this.api.post('/auth/login', credentials);
    
    if (response.data.success) {
      this.setToken(response.data.data.token);
      localStorage.setItem('wisaq_user', JSON.stringify(response.data.data.user));
    }
    
    return response.data;
  }

  async logout(): Promise<void> {
    try {
      await this.api.post('/auth/logout');
    } catch (error) {
      // Ignore logout errors
    } finally {
      this.removeToken();
    }
  }

  async refreshToken(): Promise<ApiResponse<{ token: string }>> {
    const response = await this.api.post('/auth/refresh');
    return response.data;
  }

  async getMe(): Promise<ApiResponse> {
    const response = await this.api.get('/auth/me');
    return response.data;
  }

  // Generic CRUD methods
  async get<T = any>(endpoint: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.api.get(endpoint, config);
    return response.data;
  }

  async post<T = any>(endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.api.post(endpoint, data, config);
    return response.data;
  }

  async put<T = any>(endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.api.put(endpoint, data, config);
    return response.data;
  }

  async patch<T = any>(endpoint: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.api.patch(endpoint, data, config);
    return response.data;
  }

  async delete<T = any>(endpoint: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.api.delete(endpoint, config);
    return response.data;
  }

  // File upload
  async uploadFile<T = any>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });
    }

    const response = await this.api.post(endpoint, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  }

  // Products
  async getProducts(params?: Record<string, any>) {
    return this.get('/products', { params });
  }

  async getProduct(id: number) {
    return this.get(`/products/${id}`);
  }

  async createProduct(data: any) {
    return this.post('/products', data);
  }

  async updateProduct(id: number, data: any) {
    return this.put(`/products/${id}`, data);
  }

  async deleteProduct(id: number) {
    return this.delete(`/products/${id}`);
  }

  async searchProducts(query: string) {
    return this.get(`/products/search/${encodeURIComponent(query)}`);
  }

  async findProductByBarcode(barcode: string) {
    return this.get(`/products/barcode/${encodeURIComponent(barcode)}`);
  }

  // Categories
  async getCategories(params?: Record<string, any>) {
    return this.get('/categories', { params });
  }

  async createCategory(data: any) {
    return this.post('/categories', data);
  }

  async updateCategory(id: number, data: any) {
    return this.put(`/categories/${id}`, data);
  }

  async deleteCategory(id: number) {
    return this.delete(`/categories/${id}`);
  }

  // Sales
  async getSales(params?: Record<string, any>) {
    return this.get('/sales', { params });
  }

  async getSale(id: number) {
    return this.get(`/sales/${id}`);
  }

  async createSale(data: any) {
    return this.post('/pos/create-sale', data);
  }

  async processPayment(data: any) {
    return this.post('/pos/process-payment', data);
  }

  async printInvoice(id: number) {
    return this.post(`/sales/${id}/print`);
  }

  async refundSale(id: number, data: any) {
    return this.post(`/sales/${id}/refund`, data);
  }

  // Customers
  async getCustomers(params?: Record<string, any>) {
    return this.get('/customers', { params });
  }

  async createCustomer(data: any) {
    return this.post('/customers', data);
  }

  async updateCustomer(id: number, data: any) {
    return this.put(`/customers/${id}`, data);
  }

  async deleteCustomer(id: number) {
    return this.delete(`/customers/${id}`);
  }

  // Dashboard
  async getDashboardStats(params?: Record<string, any>) {
    return this.get('/reports/dashboard', { params });
  }

  async getSalesSummary(params?: Record<string, any>) {
    return this.get('/reports/sales-summary', { params });
  }

  // Branches
  async getBranches(params?: Record<string, any>) {
    return this.get('/branches', { params });
  }

  async createBranch(data: any) {
    return this.post('/branches', data);
  }

  async updateBranch(id: number, data: any) {
    return this.put(`/branches/${id}`, data);
  }

  async deleteBranch(id: number) {
    return this.delete(`/branches/${id}`);
  }

  // Users
  async getUsers(params?: Record<string, any>) {
    return this.get('/users', { params });
  }

  async createUser(data: any) {
    return this.post('/users', data);
  }

  async updateUser(id: number, data: any) {
    return this.put(`/users/${id}`, data);
  }

  async deleteUser(id: number) {
    return this.delete(`/users/${id}`);
  }

  // Companies (Super Admin only)
  async getCompanies(params?: Record<string, any>) {
    return this.get('/admin/companies', { params });
  }

  async createCompany(data: any) {
    return this.post('/admin/companies', data);
  }

  async updateCompany(id: number, data: any) {
    return this.put(`/admin/companies/${id}`, data);
  }

  async deleteCompany(id: number) {
    return this.delete(`/admin/companies/${id}`);
  }

  async suspendCompany(id: number) {
    return this.post(`/admin/companies/${id}/suspend`);
  }

  async activateCompany(id: number) {
    return this.post(`/admin/companies/${id}/activate`);
  }

  // Reports
  async getDashboardStats(params: any = {}): Promise<ApiResponse<any>> {
    const queryString = new URLSearchParams(params).toString();
    return this.get(`/reports/dashboard?${queryString}`);
  }

  async getSalesReport(params: any = {}): Promise<ApiResponse<any>> {
    const queryString = new URLSearchParams(params).toString();
    return this.get(`/reports/sales-report?${queryString}`);
  }

  async getInventoryReport(): Promise<ApiResponse<any>> {
    return this.get('/reports/inventory-report');
  }

  // Notifications
  async getNotifications(params?: Record<string, any>) {
    return this.get('/notifications', { params });
  }

  async markNotificationAsRead(id: number) {
    return this.post(`/notifications/${id}/read`);
  }

  async markAllNotificationsAsRead() {
    return this.post('/notifications/mark-all-read');
  }

  async deleteNotification(id: number) {
    return this.delete(`/notifications/${id}`);
  }

  // Subscription
  async getCurrentSubscription() {
    return this.get('/subscription/current');
  }

  async getSubscriptionPlans() {
    return this.get('/subscription/plans');
  }

  async upgradeSubscription(planId: number) {
    return this.post('/subscription/upgrade', { plan_id: planId });
  }

  async cancelSubscription() {
    return this.post('/subscription/cancel');
  }

  async renewSubscription() {
    return this.post('/subscription/renew');
  }

  async getPaymentHistory() {
    return this.get('/subscription/payment-history');
  }

  // Payments
  async createPayment(data: any) {
    return this.post('/payments/create', data);
  }

  async verifyPayment(paymentId: number) {
    return this.get(`/payments/${paymentId}/verify`);
  }
}

export const apiService = new ApiService();
export default apiService;
