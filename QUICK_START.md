# دليل البدء السريع - نظام وثاق لنقاط البيع

## 🚀 البدء السريع (5 دقائق)

### المتطلبات الأساسية
- PHP 8.2+
- MySQL 8.0+
- Composer
- Node.js 18+
- XAMPP أو WAMP (للتطوير المحلي)

### 1. تشغيل قاعدة البيانات
```bash
# تشغيل XAMPP
# تأكد من تشغيل Apache و MySQL
```

### 2. تشغيل Backend
```bash
cd wisaq-pos-backend

# تثبيت الحزم
composer install

# تشغيل Migrations
php artisan migrate:fresh --seed

# تشغيل الخادم
php artisan serve
```

### 3. تشغيل Frontend
```bash
cd wisaq-pos-frontend

# تثبيت الحزم
npm install

# تشغيل التطبيق
npm run dev
```

### 4. الوصول للنظام
- **Backend API**: http://localhost:8000
- **Frontend**: http://localhost:5173
- **قاعدة البيانات**: wisaq_pos

### 5. بيانات تسجيل الدخول
- **البريد**: <EMAIL>
- **كلمة المرور**: Admin@123456

## 🔧 اختبار النظام

### اختبار API
```bash
# اختبار تسجيل الدخول
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Admin@123456"}'

# اختبار الحصول على المستخدم
curl -X GET http://localhost:8000/api/auth/me \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### اختبار قاعدة البيانات
```sql
-- الاتصال بقاعدة البيانات
mysql -u root -p

-- استخدام قاعدة البيانات
USE wisaq_pos;

-- عرض الجداول
SHOW TABLES;

-- عرض المستخدمين
SELECT * FROM users;

-- عرض الإعدادات
SELECT * FROM settings;
```

## 📊 الميزات المتاحة حالياً

### ✅ Backend API
- **المصادقة**: تسجيل دخول/خروج
- **إدارة المستخدمين**: CRUD كامل
- **إدارة الشركات**: CRUD كامل
- **إدارة الفروع**: CRUD كامل
- **إدارة المنتجات**: CRUD كامل مع باركود
- **إدارة العملاء**: CRUD كامل
- **إدارة الموردين**: CRUD كامل
- **نقاط البيع**: معالجة المبيعات
- **التقارير**: إحصائيات أساسية
- **الأمان**: حماية متقدمة
- **سجلات المراجعة**: تتبع شامل

### ✅ قاعدة البيانات
- **25+ جدول** مُعد بالكامل
- **العلاقات** محددة ومُحسنة
- **البيانات الأساسية** مُدخلة
- **الفهارس** محسنة للأداء

### ✅ الأمان
- **Rate Limiting**: حماية من الطلبات المتكررة
- **XSS Protection**: حماية من البرمجة الضارة
- **SQL Injection Protection**: حماية من حقن SQL
- **CSRF Protection**: حماية من التزوير
- **IP Security**: حظر العناوين المشبوهة
- **Audit Logging**: تسجيل جميع العمليات

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات
```bash
# تحقق من إعدادات .env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=wisaq_pos
DB_USERNAME=root
DB_PASSWORD=

# تأكد من تشغيل MySQL
# إنشاء قاعدة البيانات يدوياً إذا لزم الأمر
```

#### 2. خطأ في Composer
```bash
# تحديث Composer
composer self-update

# مسح الكاش
composer clear-cache

# إعادة التثبيت
rm -rf vendor
composer install
```

#### 3. خطأ في المفاتيح
```bash
# توليد مفتاح التطبيق
php artisan key:generate

# توليد مفتاح JWT
php artisan jwt:secret
```

#### 4. خطأ في Permissions
```bash
# إعطاء صلاحيات للمجلدات
chmod -R 775 storage
chmod -R 775 bootstrap/cache
```

## 📝 ملاحظات مهمة

### الأمان
- **لا تستخدم** بيانات الدخول الافتراضية في الإنتاج
- **غير كلمة المرور** فور تسجيل الدخول الأول
- **فعل HTTPS** في الإنتاج
- **راجع إعدادات الأمان** في config/security.php

### الأداء
- **استخدم Redis** للكاش في الإنتاج
- **فعل OPcache** لـ PHP
- **استخدم CDN** للملفات الثابتة
- **راقب استخدام الذاكرة** والمعالج

### النسخ الاحتياطي
- **فعل النسخ الاحتياطي** التلقائي
- **اختبر استعادة البيانات** دورياً
- **احفظ النسخ** في مواقع متعددة

## 🛠️ أدوات التطوير

### Laravel Artisan Commands
```bash
# عرض جميع الأوامر
php artisan list

# عرض Routes
php artisan route:list

# تنظيف الكاش
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# إنشاء مكونات جديدة
php artisan make:controller ExampleController
php artisan make:model Example
php artisan make:migration create_examples_table
```

### قاعدة البيانات
```bash
# تشغيل migrations
php artisan migrate

# إعادة تشغيل migrations
php artisan migrate:fresh

# تشغيل seeders
php artisan db:seed

# إنشاء seeder جديد
php artisan make:seeder ExampleSeeder
```

### الأمان
```bash
# عرض سجلات الأمان
php artisan security:logs

# تنظيف السجلات القديمة
php artisan security:cleanup-logs --days=90

# عرض العناوين المحظورة
php artisan security:blocked-ips
```

## 📞 الحصول على المساعدة

### الوثائق
- **Laravel**: https://laravel.com/docs
- **React**: https://react.dev
- **TypeScript**: https://typescriptlang.org
- **Tailwind CSS**: https://tailwindcss.com

### الدعم الفني
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 50 000 0000
- **الموقع**: https://wisaq.com

### المجتمع
- **GitHub**: https://github.com/wisaq-pos
- **Discord**: https://discord.gg/wisaq
- **Telegram**: https://t.me/wisaq_support

---

**مبروك! النظام جاهز للاستخدام والتطوير** 🎉
