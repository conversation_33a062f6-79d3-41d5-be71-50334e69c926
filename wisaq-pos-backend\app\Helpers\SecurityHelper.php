<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SecurityHelper
{
    /**
     * Check if IP is currently blocked
     */
    public static function isIPBlocked(string $ip): bool
    {
        $key = "ip_blocked:{$ip}";
        return Cache::has($key);
    }

    /**
     * Block IP address temporarily
     */
    public static function blockIP(string $ip, int $minutes = 60): void
    {
        $key = "ip_blocked:{$ip}";
        $blockedUntil = time() + ($minutes * 60);
        
        Cache::put($key, $blockedUntil, now()->addMinutes($minutes));
        
        Log::warning('IP address blocked', [
            'ip' => $ip,
            'duration_minutes' => $minutes,
            'blocked_until' => date('Y-m-d H:i:s', $blockedUntil)
        ]);
    }

    /**
     * Unblock IP address
     */
    public static function unblockIP(string $ip): void
    {
        $key = "ip_blocked:{$ip}";
        Cache::forget($key);
        
        Log::info('IP address unblocked', ['ip' => $ip]);
    }

    /**
     * Get failed login attempts count for IP
     */
    public static function getFailedAttempts(string $ip): int
    {
        $key = "failed_attempts:{$ip}";
        return Cache::get($key, 0);
    }

    /**
     * Increment failed login attempts for IP
     */
    public static function incrementFailedAttempts(string $ip): int
    {
        $key = "failed_attempts:{$ip}";
        $attempts = Cache::get($key, 0) + 1;
        
        Cache::put($key, $attempts, now()->addHour());
        
        return $attempts;
    }

    /**
     * Clear failed login attempts for IP
     */
    public static function clearFailedAttempts(string $ip): void
    {
        $key = "failed_attempts:{$ip}";
        Cache::forget($key);
    }

    /**
     * Generate secure session ID
     */
    public static function generateSecureSessionId(): string
    {
        return hash('sha256', Str::random(40) . microtime(true) . random_int(1000, 9999));
    }

    /**
     * Validate session security
     */
    public static function validateSession(string $sessionId, string $userAgent, string $ip): bool
    {
        $key = "session_security:{$sessionId}";
        $sessionData = Cache::get($key);
        
        if (!$sessionData) {
            return false;
        }
        
        return $sessionData['user_agent'] === $userAgent && $sessionData['ip'] === $ip;
    }

    /**
     * Store session security data
     */
    public static function storeSessionSecurity(string $sessionId, string $userAgent, string $ip): void
    {
        $key = "session_security:{$sessionId}";
        $data = [
            'user_agent' => $userAgent,
            'ip' => $ip,
            'created_at' => time()
        ];
        
        Cache::put($key, $data, now()->addHours(24));
    }

    /**
     * Check if request is from suspicious source
     */
    public static function isSuspiciousRequest(string $userAgent, string $ip): bool
    {
        // Check for common bot user agents
        $suspiciousAgents = [
            'bot', 'crawler', 'spider', 'scraper', 'curl', 'wget', 'python', 'java'
        ];
        
        foreach ($suspiciousAgents as $agent) {
            if (stripos($userAgent, $agent) !== false) {
                return true;
            }
        }
        
        // Check for suspicious IP patterns
        $suspiciousIPs = [
            '127.0.0.1', // Localhost (in production)
            '0.0.0.0',   // Invalid IP
        ];
        
        if (in_array($ip, $suspiciousIPs) && app()->environment('production')) {
            return true;
        }
        
        return false;
    }

    /**
     * Generate secure API token
     */
    public static function generateApiToken(int $userId): string
    {
        $payload = [
            'user_id' => $userId,
            'timestamp' => time(),
            'random' => Str::random(16)
        ];
        
        $token = base64_encode(json_encode($payload));
        $signature = hash_hmac('sha256', $token, config('app.key'));
        
        return $token . '.' . $signature;
    }

    /**
     * Verify API token
     */
    public static function verifyApiToken(string $token): ?array
    {
        $parts = explode('.', $token);
        
        if (count($parts) !== 2) {
            return null;
        }
        
        [$payload, $signature] = $parts;
        
        // Verify signature
        $expectedSignature = hash_hmac('sha256', $payload, config('app.key'));
        if (!hash_equals($expectedSignature, $signature)) {
            return null;
        }
        
        // Decode payload
        $data = json_decode(base64_decode($payload), true);
        
        if (!$data || !isset($data['user_id'], $data['timestamp'])) {
            return null;
        }
        
        // Check if token is expired (24 hours)
        if (time() - $data['timestamp'] > 86400) {
            return null;
        }
        
        return $data;
    }

    /**
     * Sanitize filename for security
     */
    public static function sanitizeFilename(string $filename): string
    {
        // Remove directory traversal attempts
        $filename = str_replace(['../', '..\\', '../', '..\\'], '', $filename);
        
        // Remove null bytes
        $filename = str_replace("\0", '', $filename);
        
        // Remove or replace dangerous characters
        $filename = preg_replace('/[<>:"|?*]/', '', $filename);
        
        // Limit length
        if (strlen($filename) > 255) {
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            $name = pathinfo($filename, PATHINFO_FILENAME);
            $filename = substr($name, 0, 255 - strlen($extension) - 1) . '.' . $extension;
        }
        
        return $filename;
    }

    /**
     * Check if file type is allowed
     */
    public static function isAllowedFileType(string $filename, string $mimeType): bool
    {
        $allowedExtensions = [
            'jpg', 'jpeg', 'png', 'gif', 'webp',
            'pdf', 'doc', 'docx', 'xls', 'xlsx',
            'txt', 'csv'
        ];
        
        $allowedMimeTypes = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain', 'text/csv'
        ];
        
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        
        return in_array($extension, $allowedExtensions) && in_array($mimeType, $allowedMimeTypes);
    }

    /**
     * Generate secure random string
     */
    public static function generateSecureRandom(int $length = 32): string
    {
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * Hash sensitive data
     */
    public static function hashSensitiveData(string $data): string
    {
        return hash('sha256', $data . config('app.key'));
    }

    /**
     * Mask sensitive data for logging
     */
    public static function maskSensitiveData(string $data, int $visibleChars = 4): string
    {
        $length = strlen($data);
        
        if ($length <= $visibleChars) {
            return str_repeat('*', $length);
        }
        
        $masked = str_repeat('*', $length - $visibleChars);
        return $masked . substr($data, -$visibleChars);
    }

    /**
     * Log security event
     */
    public static function logSecurityEvent(string $event, array $context = []): void
    {
        Log::channel('security')->warning($event, array_merge($context, [
            'timestamp' => now()->toISOString(),
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
        ]));
    }
}
