<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Setting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class SecuritySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء الأدوار الأساسية
        $this->createRoles();

        // إنشاء الصلاحيات
        $this->createPermissions();

        // ربط الصلاحيات بالأدوار
        $this->assignPermissionsToRoles();

        // إنشاء المستخدم الرئيسي
        $this->createSuperAdmin();

        // إنشاء الإعدادات الأساسية
        $this->createSettings();
    }

    /**
     * إنشاء الأدوار الأساسية
     */
    private function createRoles(): void
    {
        $roles = [
            'super_admin' => 'مدير عام',
            'admin' => 'مدير',
            'supervisor' => 'مشرف',
            'manager' => 'مدير فرع',
            'cashier' => 'كاشير',
            'accountant' => 'محاسب',
        ];

        foreach ($roles as $name => $displayName) {
            Role::firstOrCreate(
                ['name' => $name],
                ['display_name' => $displayName]
            );
        }
    }

    /**
     * إنشاء الصلاحيات
     */
    private function createPermissions(): void
    {
        $permissions = [
            // إدارة الشركات
            'companies.view' => 'عرض الشركات',
            'companies.create' => 'إنشاء شركة',
            'companies.edit' => 'تعديل الشركات',
            'companies.delete' => 'حذف الشركات',
            'companies.suspend' => 'تعليق الشركات',

            // إدارة الفروع
            'branches.view' => 'عرض الفروع',
            'branches.create' => 'إنشاء فرع',
            'branches.edit' => 'تعديل الفروع',
            'branches.delete' => 'حذف الفروع',

            // إدارة المستخدمين
            'users.view' => 'عرض المستخدمين',
            'users.create' => 'إنشاء مستخدم',
            'users.edit' => 'تعديل المستخدمين',
            'users.delete' => 'حذف المستخدمين',
            'users.activate' => 'تفعيل المستخدمين',
            'users.deactivate' => 'إلغاء تفعيل المستخدمين',

            // إدارة المنتجات
            'products.view' => 'عرض المنتجات',
            'products.create' => 'إنشاء منتج',
            'products.edit' => 'تعديل المنتجات',
            'products.delete' => 'حذف المنتجات',
            'products.manage_stock' => 'إدارة المخزون',

            // إدارة المبيعات
            'sales.view' => 'عرض المبيعات',
            'sales.create' => 'إنشاء مبيعة',
            'sales.edit' => 'تعديل المبيعات',
            'sales.delete' => 'حذف المبيعات',
            'sales.refund' => 'استرداد المبيعات',

            // نقاط البيع
            'pos.access' => 'الوصول لنقاط البيع',
            'pos.process_sale' => 'معالجة المبيعات',
            'pos.apply_discount' => 'تطبيق خصومات',

            // التقارير
            'reports.view' => 'عرض التقارير',
            'reports.export' => 'تصدير التقارير',
            'reports.financial' => 'التقارير المالية',

            // إدارة العملاء
            'customers.view' => 'عرض العملاء',
            'customers.create' => 'إنشاء عميل',
            'customers.edit' => 'تعديل العملاء',
            'customers.delete' => 'حذف العملاء',

            // إدارة الموردين
            'suppliers.view' => 'عرض الموردين',
            'suppliers.create' => 'إنشاء مورد',
            'suppliers.edit' => 'تعديل الموردين',
            'suppliers.delete' => 'حذف الموردين',

            // إدارة الاشتراكات
            'subscriptions.view' => 'عرض الاشتراكات',
            'subscriptions.manage' => 'إدارة الاشتراكات',
            'subscriptions.payments' => 'مدفوعات الاشتراكات',

            // الإعدادات
            'settings.view' => 'عرض الإعدادات',
            'settings.edit' => 'تعديل الإعدادات',
            'settings.system' => 'إعدادات النظام',

            // الأمان
            'security.audit_logs' => 'سجلات المراجعة',
            'security.manage_roles' => 'إدارة الأدوار',
            'security.manage_permissions' => 'إدارة الصلاحيات',
        ];

        foreach ($permissions as $name => $displayName) {
            Permission::firstOrCreate(
                ['name' => $name],
                ['display_name' => $displayName]
            );
        }
    }

    /**
     * ربط الصلاحيات بالأدوار
     */
    private function assignPermissionsToRoles(): void
    {
        // صلاحيات المدير العام
        $superAdmin = Role::findByName('super_admin');
        $superAdmin->givePermissionTo(Permission::all());

        // صلاحيات المدير
        $admin = Role::findByName('admin');
        $adminPermissions = [
            'branches.view', 'branches.create', 'branches.edit',
            'users.view', 'users.create', 'users.edit', 'users.activate', 'users.deactivate',
            'products.view', 'products.create', 'products.edit', 'products.manage_stock',
            'sales.view', 'sales.create', 'sales.edit', 'sales.refund',
            'pos.access', 'pos.process_sale', 'pos.apply_discount',
            'reports.view', 'reports.export', 'reports.financial',
            'customers.view', 'customers.create', 'customers.edit',
            'suppliers.view', 'suppliers.create', 'suppliers.edit',
            'subscriptions.view', 'subscriptions.manage',
            'settings.view', 'settings.edit',
        ];
        $admin->givePermissionTo($adminPermissions);

        // صلاحيات المشرف
        $supervisor = Role::findByName('supervisor');
        $supervisorPermissions = [
            'users.view', 'products.view', 'sales.view', 'reports.view',
            'customers.view', 'suppliers.view', 'pos.access'
        ];
        $supervisor->givePermissionTo($supervisorPermissions);

        // صلاحيات مدير الفرع
        $manager = Role::findByName('manager');
        $managerPermissions = [
            'users.view', 'users.create', 'users.edit',
            'products.view', 'products.create', 'products.edit', 'products.manage_stock',
            'sales.view', 'sales.create', 'sales.edit',
            'pos.access', 'pos.process_sale', 'pos.apply_discount',
            'reports.view', 'reports.export',
            'customers.view', 'customers.create', 'customers.edit',
            'suppliers.view', 'suppliers.create', 'suppliers.edit',
        ];
        $manager->givePermissionTo($managerPermissions);

        // صلاحيات الكاشير
        $cashier = Role::findByName('cashier');
        $cashierPermissions = [
            'products.view', 'sales.view', 'sales.create',
            'pos.access', 'pos.process_sale',
            'customers.view', 'customers.create'
        ];
        $cashier->givePermissionTo($cashierPermissions);

        // صلاحيات المحاسب
        $accountant = Role::findByName('accountant');
        $accountantPermissions = [
            'sales.view', 'reports.view', 'reports.export', 'reports.financial',
            'customers.view', 'suppliers.view'
        ];
        $accountant->givePermissionTo($accountantPermissions);
    }

    /**
     * إنشاء المستخدم الرئيسي
     */
    private function createSuperAdmin(): void
    {
        $superAdmin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'مدير النظام',
                'phone' => '+************',
                'password' => Hash::make('Admin@123456'),
                'user_type' => 'super_admin',
                'status' => 'active',
                'email_verified_at' => now(),
                'password_changed_at' => now(),
            ]
        );

        $superAdmin->assignRole('super_admin');
    }

    /**
     * إنشاء الإعدادات الأساسية
     */
    private function createSettings(): void
    {
        $settings = [
            'app_name' => 'Wisaq POS',
            'app_name_ar' => 'نظام وثاق لنقاط البيع',
            'app_description' => 'نظام نقاط بيع متكامل للمحلات والشركات',
            'app_version' => '1.0.0',
            'default_language' => 'ar',
            'default_currency' => 'SAR',
            'default_timezone' => 'Asia/Riyadh',
            'tax_rate' => '15.00',
            'tax_number' => '',
            'company_address' => '',
            'company_phone' => '',
            'company_email' => '',
            'smtp_host' => '',
            'smtp_port' => '587',
            'smtp_username' => '',
            'smtp_password' => '',
            'smtp_encryption' => 'tls',
            'tap_secret_key' => '',
            'tap_public_key' => '',
            'tap_webhook_url' => '',
            'subscription_base_price' => '99.00',
            'subscription_additional_branch_price' => '29.70',
            'max_failed_login_attempts' => '5',
            'account_lockout_duration' => '30',
            'password_min_length' => '8',
            'password_require_uppercase' => '1',
            'password_require_lowercase' => '1',
            'password_require_numbers' => '1',
            'password_require_symbols' => '1',
            'session_timeout' => '120',
            'backup_enabled' => '1',
            'backup_frequency' => 'daily',
            'maintenance_mode' => '0',
        ];

        foreach ($settings as $key => $value) {
            Setting::firstOrCreate(
                ['key' => $key],
                [
                    'value' => $value,
                    'type' => 'string',
                    'group' => $this->getSettingGroup($key),
                ]
            );
        }
    }

    /**
     * تحديد مجموعة الإعداد
     */
    private function getSettingGroup(string $key): string
    {
        $groups = [
            'app_' => 'application',
            'default_' => 'application',
            'tax_' => 'financial',
            'company_' => 'company',
            'smtp_' => 'email',
            'tap_' => 'payment',
            'subscription_' => 'subscription',
            'max_failed_' => 'security',
            'account_' => 'security',
            'password_' => 'security',
            'session_' => 'security',
            'backup_' => 'system',
            'maintenance_' => 'system',
        ];

        foreach ($groups as $prefix => $group) {
            if (str_starts_with($key, $prefix)) {
                return $group;
            }
        }

        return 'general';
    }
}
