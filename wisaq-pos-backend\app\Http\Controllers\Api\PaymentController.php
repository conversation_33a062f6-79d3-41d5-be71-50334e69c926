<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\Subscription;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class PaymentController extends Controller
{
    private $tapApiUrl;
    private $tapSecretKey;

    public function __construct()
    {
        $this->tapApiUrl = config('services.tap.api_url', 'https://api.tap.company/v2');
        $this->tapSecretKey = config('services.tap.secret_key');
    }

    /**
     * إنشاء دفعة جديدة
     */
    public function createPayment(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'amount' => 'required|numeric|min:1',
                'currency' => 'required|string|in:SAR,USD,AED',
                'description' => 'required|string|max:255',
                'subscription_id' => 'required|exists:subscriptions,id',
                'customer_email' => 'required|email',
                'customer_phone' => 'required|string',
                'redirect_url' => 'required|url',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $companyId = Auth::user()->company_id;
            $subscription = Subscription::where('id', $request->subscription_id)
                ->where('company_id', $companyId)
                ->first();

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'الاشتراك غير موجود'
                ], 404);
            }

            // إنشاء الدفعة في قاعدة البيانات
            $payment = Payment::create([
                'company_id' => $companyId,
                'subscription_id' => $subscription->id,
                'amount' => $request->amount,
                'currency' => $request->currency,
                'description' => $request->description,
                'status' => 'pending',
                'payment_method' => 'tap',
                'reference_number' => 'PAY-' . time() . '-' . rand(1000, 9999),
            ]);

            // إنشاء الدفعة في Tap
            $tapPayment = $this->createTapPayment([
                'amount' => $request->amount,
                'currency' => $request->currency,
                'description' => $request->description,
                'reference' => $payment->reference_number,
                'customer' => [
                    'email' => $request->customer_email,
                    'phone' => $request->customer_phone,
                ],
                'redirect_url' => $request->redirect_url,
                'metadata' => [
                    'payment_id' => $payment->id,
                    'company_id' => $companyId,
                    'subscription_id' => $subscription->id,
                ]
            ]);

            if (!$tapPayment['success']) {
                $payment->update(['status' => 'failed']);
                return response()->json([
                    'success' => false,
                    'message' => 'خطأ في إنشاء الدفعة'
                ], 500);
            }

            // تحديث معرف Tap
            $payment->update([
                'tap_payment_id' => $tapPayment['data']['id'],
                'payment_url' => $tapPayment['data']['transaction']['url']
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء الدفعة بنجاح',
                'data' => [
                    'payment_id' => $payment->id,
                    'payment_url' => $tapPayment['data']['transaction']['url'],
                    'tap_payment_id' => $tapPayment['data']['id']
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Payment creation error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء الدفعة'
            ], 500);
        }
    }

    /**
     * التحقق من حالة الدفعة
     */
    public function verifyPayment($paymentId): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $payment = Payment::where('id', $paymentId)
                ->where('company_id', $companyId)
                ->first();

            if (!$payment) {
                return response()->json([
                    'success' => false,
                    'message' => 'الدفعة غير موجودة'
                ], 404);
            }

            // التحقق من حالة الدفعة في Tap
            $tapPayment = $this->getTapPayment($payment->tap_payment_id);

            if (!$tapPayment['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'خطأ في التحقق من الدفعة'
                ], 500);
            }

            $tapStatus = $tapPayment['data']['status'];
            $newStatus = $this->mapTapStatus($tapStatus);

            // تحديث حالة الدفعة
            $payment->update([
                'status' => $newStatus,
                'tap_response' => $tapPayment['data'],
                'paid_at' => $newStatus === 'completed' ? now() : null
            ]);

            // إذا تمت الدفعة بنجاح، تحديث الاشتراك
            if ($newStatus === 'completed') {
                $this->updateSubscription($payment);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'payment' => $payment,
                    'tap_status' => $tapStatus,
                    'status' => $newStatus
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Payment verification error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'خطأ في التحقق من الدفعة'
            ], 500);
        }
    }

    /**
     * معالجة webhook من Tap
     */
    public function handleWebhook(Request $request): JsonResponse
    {
        try {
            $payload = $request->all();
            Log::info('Tap webhook received', $payload);

            // التحقق من صحة الـ webhook
            if (!$this->verifyWebhookSignature($request)) {
                Log::warning('Invalid webhook signature');
                return response()->json(['success' => false], 400);
            }

            $tapPaymentId = $payload['id'] ?? null;
            $status = $payload['status'] ?? null;

            if (!$tapPaymentId || !$status) {
                Log::warning('Missing payment ID or status in webhook');
                return response()->json(['success' => false], 400);
            }

            // البحث عن الدفعة
            $payment = Payment::where('tap_payment_id', $tapPaymentId)->first();

            if (!$payment) {
                Log::warning('Payment not found for Tap ID: ' . $tapPaymentId);
                return response()->json(['success' => false], 404);
            }

            $newStatus = $this->mapTapStatus($status);

            // تحديث حالة الدفعة
            $payment->update([
                'status' => $newStatus,
                'tap_response' => $payload,
                'paid_at' => $newStatus === 'completed' ? now() : null
            ]);

            // إذا تمت الدفعة بنجاح، تحديث الاشتراك
            if ($newStatus === 'completed') {
                $this->updateSubscription($payment);
            }

            Log::info('Payment updated successfully', [
                'payment_id' => $payment->id,
                'status' => $newStatus
            ]);

            return response()->json(['success' => true]);

        } catch (\Exception $e) {
            Log::error('Webhook processing error: ' . $e->getMessage());
            return response()->json(['success' => false], 500);
        }
    }

    /**
     * إنشاء دفعة في Tap
     */
    private function createTapPayment(array $data): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->tapSecretKey,
                'Content-Type' => 'application/json',
            ])->post($this->tapApiUrl . '/charges', [
                'amount' => $data['amount'],
                'currency' => $data['currency'],
                'description' => $data['description'],
                'reference' => [
                    'transaction' => $data['reference'],
                    'order' => $data['reference']
                ],
                'customer' => [
                    'email' => $data['customer']['email'],
                    'phone' => [
                        'country_code' => '966',
                        'number' => $data['customer']['phone']
                    ]
                ],
                'redirect' => [
                    'url' => $data['redirect_url']
                ],
                'metadata' => $data['metadata'] ?? []
            ]);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            }

            Log::error('Tap API error', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [
                'success' => false,
                'error' => $response->body()
            ];

        } catch (\Exception $e) {
            Log::error('Tap API exception: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على دفعة من Tap
     */
    private function getTapPayment(string $tapPaymentId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->tapSecretKey,
            ])->get($this->tapApiUrl . '/charges/' . $tapPaymentId);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'data' => $response->json()
                ];
            }

            return [
                'success' => false,
                'error' => $response->body()
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * تحويل حالة Tap إلى حالة النظام
     */
    private function mapTapStatus(string $tapStatus): string
    {
        switch (strtolower($tapStatus)) {
            case 'captured':
            case 'authorized':
                return 'completed';
            case 'failed':
            case 'declined':
                return 'failed';
            case 'cancelled':
                return 'cancelled';
            case 'pending':
            default:
                return 'pending';
        }
    }

    /**
     * التحقق من توقيع الـ webhook
     */
    private function verifyWebhookSignature(Request $request): bool
    {
        // يمكن تطبيق التحقق من التوقيع هنا حسب وثائق Tap
        return true;
    }

    /**
     * تحديث الاشتراك بعد الدفع الناجح
     */
    private function updateSubscription(Payment $payment): void
    {
        $subscription = $payment->subscription;
        $company = $subscription->company;

        // تحديث تاريخ انتهاء الاشتراك
        $newExpiryDate = $subscription->expires_at && $subscription->expires_at->isFuture()
            ? $subscription->expires_at->addMonth()
            : now()->addMonth();

        $subscription->update([
            'status' => 'active',
            'expires_at' => $newExpiryDate,
            'last_payment_at' => now()
        ]);

        // تفعيل الشركة
        $company->update([
            'status' => 'active',
            'subscription_status' => 'active'
        ]);

        Log::info('Subscription updated after successful payment', [
            'subscription_id' => $subscription->id,
            'company_id' => $company->id,
            'expires_at' => $newExpiryDate
        ]);
    }
}
