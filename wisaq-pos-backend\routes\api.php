<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\CompanyController;
use App\Http\Controllers\Api\BranchController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\CustomerController;
use App\Http\Controllers\Api\SupplierController;
use App\Http\Controllers\Api\SaleController;
use App\Http\Controllers\Api\ReportController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\SubscriptionController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\SecurityController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes (لا تحتاج مصادقة)
Route::prefix('auth')->group(function () {
    Route::post('login', [AuthController::class, 'login']);
});

// Protected routes (تحتاج مصادقة)
Route::middleware(['auth:api'])->group(function () {
    
    // Auth routes
    Route::prefix('auth')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('refresh', [AuthController::class, 'refresh']);
        Route::get('me', [AuthController::class, 'me']);
    });

    // Super Admin routes
    Route::middleware(['role:super_admin'])->prefix('admin')->group(function () {
        Route::apiResource('companies', CompanyController::class);
        Route::post('companies/{company}/suspend', [CompanyController::class, 'suspend']);
        Route::post('companies/{company}/activate', [CompanyController::class, 'activate']);
    });

    // Company-specific routes
    Route::middleware(['company.access', 'subscription.check'])->group(function () {
        
        // Branch management
        Route::apiResource('branches', BranchController::class);
        Route::post('branches/{branch}/set-main', [BranchController::class, 'setAsMain']);
        
        // User management
        Route::apiResource('users', UserController::class);
        Route::post('users/{user}/activate', [UserController::class, 'activate']);
        Route::post('users/{user}/deactivate', [UserController::class, 'deactivate']);
        Route::post('users/{user}/reset-password', [UserController::class, 'resetPassword']);
        
        // Category management
        Route::apiResource('categories', CategoryController::class);
        Route::post('categories/{category}/toggle-status', [CategoryController::class, 'toggleStatus']);
        
        // Product management
        Route::apiResource('products', ProductController::class);
        Route::post('products/{product}/toggle-status', [ProductController::class, 'toggleStatus']);
        Route::post('products/{product}/toggle-featured', [ProductController::class, 'toggleFeatured']);
        Route::post('products/{product}/update-quantity', [ProductController::class, 'updateQuantity']);
        Route::get('products/search/{query}', [ProductController::class, 'search']);
        Route::get('products/barcode/{barcode}', [ProductController::class, 'findByBarcode']);
        Route::get('products/low-stock', [ProductController::class, 'lowStock']);
        Route::get('products/expired', [ProductController::class, 'expired']);
        Route::get('products/expiring-soon', [ProductController::class, 'expiringSoon']);
        
        // Customer management
        Route::apiResource('customers', CustomerController::class);
        Route::post('customers/{customer}/toggle-status', [CustomerController::class, 'toggleStatus']);
        Route::get('customers/search/{query}', [CustomerController::class, 'search']);
        
        // Supplier management
        Route::apiResource('suppliers', SupplierController::class);
        Route::post('suppliers/{supplier}/toggle-status', [SupplierController::class, 'toggleStatus']);
        Route::get('suppliers/search/{query}', [SupplierController::class, 'search']);
        
        // Sales management
        Route::apiResource('sales', SaleController::class);
        Route::post('sales/{sale}/print', [SaleController::class, 'print']);
        Route::post('sales/{sale}/refund', [SaleController::class, 'refund']);
        Route::get('sales/invoice/{invoiceNumber}', [SaleController::class, 'findByInvoice']);
        
        // POS routes
        Route::prefix('pos')->group(function () {
            Route::post('create-sale', [SaleController::class, 'createSale']);
            Route::post('add-item', [SaleController::class, 'addItem']);
            Route::post('remove-item', [SaleController::class, 'removeItem']);
            Route::post('apply-discount', [SaleController::class, 'applyDiscount']);
            Route::post('process-payment', [SaleController::class, 'processPayment']);
            Route::get('current-sale', [SaleController::class, 'getCurrentSale']);
        });
        
        // Reports routes
        Route::prefix('reports')->group(function () {
            Route::get('dashboard', [ReportController::class, 'dashboardStats']);
            Route::get('sales-report', [ReportController::class, 'salesReport']);
            Route::get('inventory-report', [ReportController::class, 'inventoryReport']);
            Route::get('sales-summary', [SaleController::class, 'salesSummary']);
            Route::get('top-products', [ProductController::class, 'topProducts']);
            Route::get('customer-report', [CustomerController::class, 'customerReport']);
        });

        // Subscription routes
        Route::prefix('subscription')->group(function () {
            Route::get('current', [SubscriptionController::class, 'current']);
            Route::get('plans', [SubscriptionController::class, 'plans']);
            Route::post('upgrade', [SubscriptionController::class, 'upgrade']);
            Route::post('cancel', [SubscriptionController::class, 'cancel']);
            Route::post('renew', [SubscriptionController::class, 'renew']);
            Route::get('payment-history', [SubscriptionController::class, 'paymentHistory']);
        });

        // Payment routes
        Route::prefix('payments')->group(function () {
            Route::post('create', [PaymentController::class, 'createPayment']);
            Route::get('{payment}/verify', [PaymentController::class, 'verifyPayment']);
        });

        // Notification routes
        Route::prefix('notifications')->group(function () {
            Route::get('/', [NotificationController::class, 'index']);
            Route::post('{notification}/read', [NotificationController::class, 'markAsRead']);
            Route::post('mark-all-read', [NotificationController::class, 'markAllAsRead']);
            Route::delete('{notification}', [NotificationController::class, 'destroy']);
            Route::post('generate-low-stock', [NotificationController::class, 'generateLowStockNotifications']);
            Route::post('generate-expiry', [NotificationController::class, 'generateExpiryNotifications']);
            Route::post('generate-subscription', [NotificationController::class, 'generateSubscriptionNotifications']);
        });

        // Security routes
        Route::prefix('security')->group(function () {
            Route::post('change-password', [SecurityController::class, 'changePassword']);
            Route::get('dashboard', [SecurityController::class, 'securityDashboard']);
            Route::post('validate-password', [SecurityController::class, 'validatePassword']);
            Route::post('generate-token', [SecurityController::class, 'generateToken']);
            Route::get('audit-logs', [SecurityController::class, 'auditLogs']);
            Route::post('revoke-sessions', [SecurityController::class, 'revokeAllSessions']);
            Route::post('report-incident', [SecurityController::class, 'reportIncident']);
            Route::get('settings', [SecurityController::class, 'getSecuritySettings']);
        });
    });

    // Public payment webhook (لا تحتاج مصادقة)
    Route::post('payments/webhook', [PaymentController::class, 'handleWebhook']);
});

// Fallback route
Route::fallback(function () {
    return response()->json([
        'success' => false,
        'message' => 'API endpoint not found'
    ], 404);
});
