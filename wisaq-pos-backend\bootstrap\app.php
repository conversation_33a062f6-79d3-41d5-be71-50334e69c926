<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Global middleware
        $middleware->append([
            \App\Http\Middleware\IPSecurityMiddleware::class,
            \App\Http\Middleware\XSSProtectionMiddleware::class,
            \App\Http\Middleware\SQLInjectionProtectionMiddleware::class,
        ]);

        // API middleware group
        $middleware->group('api', [
            \Laravel\Sanctum\Http\Middleware\EnsureFrontendRequestsAreStateful::class,
            'throttle:api',
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\RateLimitMiddleware::class . ':120,1', // 120 requests per minute
            \App\Http\Middleware\AuditLogMiddleware::class,
        ]);

        // Web middleware group
        $middleware->group('web', [
            \App\Http\Middleware\EncryptCookies::class,
            \Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse::class,
            \Illuminate\Session\Middleware\StartSession::class,
            \Illuminate\View\Middleware\ShareErrorsFromSession::class,
            \App\Http\Middleware\EnhancedCSRFMiddleware::class,
            \Illuminate\Routing\Middleware\SubstituteBindings::class,
            \App\Http\Middleware\AuditLogMiddleware::class,
        ]);

        // Middleware aliases
        $middleware->alias([
            'company.access' => \App\Http\Middleware\CompanyAccessMiddleware::class,
            'subscription.check' => \App\Http\Middleware\CheckSubscription::class,
            'rate.limit' => \App\Http\Middleware\RateLimitMiddleware::class,
            'xss.protection' => \App\Http\Middleware\XSSProtectionMiddleware::class,
            'sql.protection' => \App\Http\Middleware\SQLInjectionProtectionMiddleware::class,
            'ip.security' => \App\Http\Middleware\IPSecurityMiddleware::class,
            'audit.log' => \App\Http\Middleware\AuditLogMiddleware::class,
            'enhanced.csrf' => \App\Http\Middleware\EnhancedCSRFMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        //
    })->create();
