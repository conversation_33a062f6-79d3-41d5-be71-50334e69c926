<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class EnhancedCSRFMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Skip CSRF check for GET, HEAD, OPTIONS requests
        if (in_array($request->method(), ['GET', 'HEAD', 'OPTIONS'])) {
            return $next($request);
        }

        // Skip CSRF check for API routes with valid JWT token
        if ($request->is('api/*') && $request->bearerToken()) {
            return $next($request);
        }

        // Check CSRF token
        if (!$this->tokensMatch($request)) {
            Log::warning('CSRF token mismatch detected', [
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'referer' => $request->header('referer'),
                'user_id' => $request->user() ? $request->user()->id : null,
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'رمز الحماية غير صحيح. يرجى إعادة تحميل الصفحة والمحاولة مرة أخرى',
                'error_code' => 'CSRF_TOKEN_MISMATCH'
            ], 419);
        }

        // Check origin header for additional security
        if (!$this->isValidOrigin($request)) {
            Log::warning('Invalid origin detected', [
                'ip' => $request->ip(),
                'origin' => $request->header('origin'),
                'referer' => $request->header('referer'),
                'url' => $request->fullUrl(),
                'user_id' => $request->user() ? $request->user()->id : null,
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'طلب غير مصرح به من مصدر غير موثوق',
                'error_code' => 'INVALID_ORIGIN'
            ], 403);
        }

        return $next($request);
    }

    /**
     * Check if CSRF tokens match
     */
    protected function tokensMatch(Request $request): bool
    {
        $token = $request->input('_token') ?: $request->header('X-CSRF-TOKEN');
        
        if (!$token && $request->header('X-XSRF-TOKEN')) {
            $token = $request->header('X-XSRF-TOKEN');
        }

        if (!$token) {
            return false;
        }

        return hash_equals(Session::token(), $token);
    }

    /**
     * Check if the request origin is valid
     */
    protected function isValidOrigin(Request $request): bool
    {
        $origin = $request->header('origin');
        $referer = $request->header('referer');
        
        // If no origin header, check referer
        if (!$origin && $referer) {
            $origin = parse_url($referer, PHP_URL_SCHEME) . '://' . parse_url($referer, PHP_URL_HOST);
        }

        // If still no origin, allow (might be same-origin request)
        if (!$origin) {
            return true;
        }

        $allowedOrigins = [
            config('app.url'),
            'http://localhost:3000', // For development
            'http://127.0.0.1:3000', // For development
        ];

        return in_array($origin, $allowedOrigins);
    }
}
