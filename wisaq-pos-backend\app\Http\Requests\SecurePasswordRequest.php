<?php

namespace App\Http\Requests;

use App\Services\SecurityService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;

class SecurePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $config = config('security.password');
        
        return [
            'password' => [
                'required',
                'string',
                Password::min($config['min_length'])
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised(),
                'confirmed',
                function ($attribute, $value, $fail) {
                    $securityService = new SecurityService();
                    $validation = $securityService->validatePasswordStrength($value);
                    
                    if (!$validation['valid']) {
                        foreach ($validation['errors'] as $error) {
                            $fail($error);
                        }
                    }
                }
            ],
            'password_confirmation' => 'required|string',
            'current_password' => 'sometimes|required|string|current_password',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'password.required' => 'كلمة المرور مطلوبة',
            'password.min' => 'كلمة المرور يجب أن تكون على الأقل :min أحرف',
            'password.letters' => 'كلمة المرور يجب أن تحتوي على أحرف',
            'password.mixed_case' => 'كلمة المرور يجب أن تحتوي على أحرف كبيرة وصغيرة',
            'password.numbers' => 'كلمة المرور يجب أن تحتوي على أرقام',
            'password.symbols' => 'كلمة المرور يجب أن تحتوي على رموز خاصة',
            'password.uncompromised' => 'كلمة المرور هذه تم اختراقها من قبل، يرجى اختيار كلمة مرور أخرى',
            'password.confirmed' => 'تأكيد كلمة المرور غير متطابق',
            'password_confirmation.required' => 'تأكيد كلمة المرور مطلوب',
            'current_password.required' => 'كلمة المرور الحالية مطلوبة',
            'current_password.current_password' => 'كلمة المرور الحالية غير صحيحة',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'password' => 'كلمة المرور',
            'password_confirmation' => 'تأكيد كلمة المرور',
            'current_password' => 'كلمة المرور الحالية',
        ];
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        // Log failed password validation attempt
        \Log::channel('security')->warning('Password validation failed', [
            'ip' => $this->ip(),
            'user_agent' => $this->userAgent(),
            'errors' => $validator->errors()->toArray(),
            'user_id' => auth()->id(),
        ]);

        parent::failedValidation($validator);
    }
}
