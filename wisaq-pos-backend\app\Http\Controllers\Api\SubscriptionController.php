<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\Company;
use App\Models\SubscriptionPlan;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class SubscriptionController extends Controller
{
    /**
     * عرض تفاصيل الاشتراك الحالي
     */
    public function current(): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $company = Company::with(['subscription.plan'])->find($companyId);

            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'الشركة غير موجودة'
                ], 404);
            }

            $subscription = $company->subscription;

            if (!$subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يوجد اشتراك نشط'
                ], 404);
            }

            // حساب الاستخدام الحالي
            $currentUsage = [
                'branches' => $company->branches()->count(),
                'users' => $company->users()->count(),
                'products' => $company->products()->count(),
                'monthly_sales' => $company->sales()
                    ->whereMonth('sale_date', now()->month)
                    ->whereYear('sale_date', now()->year)
                    ->count(),
            ];

            // حساب الأيام المتبقية
            $daysRemaining = $subscription->expires_at ? 
                now()->diffInDays($subscription->expires_at, false) : 0;

            // حساب التكلفة الإضافية للفروع
            $baseBranches = $subscription->plan->max_branches ?? 1;
            $extraBranches = max(0, $currentUsage['branches'] - $baseBranches);
            $extraBranchCost = $extraBranches > 0 ? 
                ceil($extraBranches / 5) * ($subscription->plan->price * 0.3) : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'subscription' => $subscription,
                    'plan' => $subscription->plan,
                    'company' => $company,
                    'current_usage' => $currentUsage,
                    'limits' => [
                        'max_branches' => $subscription->plan->max_branches ?? 999,
                        'max_users' => $subscription->plan->max_users ?? 999,
                        'max_products' => $subscription->plan->max_products ?? 999,
                    ],
                    'billing' => [
                        'base_price' => $subscription->plan->price,
                        'extra_branch_cost' => $extraBranchCost,
                        'total_monthly_cost' => $subscription->plan->price + $extraBranchCost,
                        'next_billing_date' => $subscription->expires_at,
                        'days_remaining' => $daysRemaining,
                        'is_expired' => $daysRemaining < 0,
                        'is_expiring_soon' => $daysRemaining <= 7 && $daysRemaining >= 0,
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب بيانات الاشتراك'
            ], 500);
        }
    }

    /**
     * عرض الخطط المتاحة
     */
    public function plans(): JsonResponse
    {
        try {
            $plans = SubscriptionPlan::where('is_active', true)
                ->orderBy('price', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $plans
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب الخطط'
            ], 500);
        }
    }

    /**
     * ترقية الاشتراك
     */
    public function upgrade(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'plan_id' => 'required|exists:subscription_plans,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $companyId = Auth::user()->company_id;
            $company = Company::with('subscription')->find($companyId);

            if (!$company) {
                return response()->json([
                    'success' => false,
                    'message' => 'الشركة غير موجودة'
                ], 404);
            }

            $newPlan = SubscriptionPlan::find($request->plan_id);
            $currentSubscription = $company->subscription;

            if (!$newPlan) {
                return response()->json([
                    'success' => false,
                    'message' => 'الخطة غير موجودة'
                ], 404);
            }

            // التحقق من أن الخطة الجديدة أفضل من الحالية
            if ($currentSubscription && $newPlan->price <= $currentSubscription->plan->price) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن التراجع إلى خطة أقل'
                ], 400);
            }

            // حساب التكلفة المتناسبة
            $daysRemaining = $currentSubscription && $currentSubscription->expires_at ? 
                now()->diffInDays($currentSubscription->expires_at, false) : 0;
            
            $proRatedAmount = $daysRemaining > 0 ? 
                ($newPlan->price - ($currentSubscription->plan->price ?? 0)) * ($daysRemaining / 30) : 
                $newPlan->price;

            // إنشاء اشتراك جديد
            $newSubscription = Subscription::create([
                'company_id' => $companyId,
                'plan_id' => $newPlan->id,
                'status' => 'pending',
                'starts_at' => now(),
                'expires_at' => $currentSubscription && $currentSubscription->expires_at && $currentSubscription->expires_at->isFuture() 
                    ? $currentSubscription->expires_at->addMonth() 
                    : now()->addMonth(),
                'price' => $newPlan->price,
                'billing_cycle' => 'monthly',
            ]);

            // إلغاء الاشتراك الحالي
            if ($currentSubscription) {
                $currentSubscription->update(['status' => 'cancelled']);
            }

            // تحديث الشركة
            $company->update([
                'subscription_id' => $newSubscription->id,
                'max_branches' => $newPlan->max_branches,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم ترقية الاشتراك بنجاح',
                'data' => [
                    'subscription' => $newSubscription->load('plan'),
                    'pro_rated_amount' => $proRatedAmount,
                    'next_billing_date' => $newSubscription->expires_at,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في ترقية الاشتراك'
            ], 500);
        }
    }

    /**
     * إلغاء الاشتراك
     */
    public function cancel(): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $company = Company::with('subscription')->find($companyId);

            if (!$company || !$company->subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يوجد اشتراك نشط'
                ], 404);
            }

            $subscription = $company->subscription;

            // تحديث حالة الاشتراك
            $subscription->update([
                'status' => 'cancelled',
                'cancelled_at' => now(),
            ]);

            // تحديث حالة الشركة (ستبقى نشطة حتى انتهاء الفترة المدفوعة)
            $company->update([
                'subscription_status' => 'cancelled'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء الاشتراك بنجاح. سيبقى الحساب نشطاً حتى ' . $subscription->expires_at->format('Y-m-d'),
                'data' => [
                    'subscription' => $subscription,
                    'expires_at' => $subscription->expires_at,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إلغاء الاشتراك'
            ], 500);
        }
    }

    /**
     * تجديد الاشتراك
     */
    public function renew(): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $company = Company::with('subscription.plan')->find($companyId);

            if (!$company || !$company->subscription) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يوجد اشتراك'
                ], 404);
            }

            $subscription = $company->subscription;

            // حساب التكلفة الإجمالية (الخطة الأساسية + الفروع الإضافية)
            $baseCost = $subscription->plan->price;
            $currentBranches = $company->branches()->count();
            $baseBranches = $subscription->plan->max_branches ?? 1;
            $extraBranches = max(0, $currentBranches - $baseBranches);
            $extraBranchCost = $extraBranches > 0 ? 
                ceil($extraBranches / 5) * ($baseCost * 0.3) : 0;
            
            $totalCost = $baseCost + $extraBranchCost;

            // إنشاء اشتراك جديد
            $newSubscription = Subscription::create([
                'company_id' => $companyId,
                'plan_id' => $subscription->plan_id,
                'status' => 'pending',
                'starts_at' => $subscription->expires_at ?? now(),
                'expires_at' => ($subscription->expires_at ?? now())->addMonth(),
                'price' => $totalCost,
                'billing_cycle' => 'monthly',
            ]);

            // تحديث الشركة
            $company->update([
                'subscription_id' => $newSubscription->id,
                'subscription_status' => 'pending'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء طلب التجديد بنجاح',
                'data' => [
                    'subscription' => $newSubscription->load('plan'),
                    'billing_details' => [
                        'base_cost' => $baseCost,
                        'extra_branches' => $extraBranches,
                        'extra_branch_cost' => $extraBranchCost,
                        'total_cost' => $totalCost,
                    ],
                    'billing_period' => [
                        'starts_at' => $newSubscription->starts_at,
                        'expires_at' => $newSubscription->expires_at,
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تجديد الاشتراك'
            ], 500);
        }
    }

    /**
     * سجل المدفوعات
     */
    public function paymentHistory(): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            
            $payments = \App\Models\Payment::where('company_id', $companyId)
                ->with('subscription.plan')
                ->orderBy('created_at', 'desc')
                ->paginate(15);

            return response()->json([
                'success' => true,
                'data' => $payments
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب سجل المدفوعات'
            ], 500);
        }
    }
}
