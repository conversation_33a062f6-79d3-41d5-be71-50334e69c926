[2025-07-13 22:37:41] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`users` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `users` add constraint `users_company_id_foreign` foreign key (`company_id`) references `companies` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`users` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `users` add constraint `users_company_id_foreign` foreign key (`company_id`) references `companies` (`id`) on delete cascade) at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`users` (errno: 150 \"Foreign key constraint is incorrectly formed\") at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#36 {main}
"} 
[2025-07-13 22:38:45] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`inventory_logs` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `inventory_logs` add constraint `inventory_logs_product_id_foreign` foreign key (`product_id`) references `products` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`inventory_logs` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `inventory_logs` add constraint `inventory_logs_product_id_foreign` foreign key (`product_id`) references `products` (`id`) on delete cascade) at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_205330_create_inventory_logs_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#45 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`inventory_logs` (errno: 150 \"Foreign key constraint is incorrectly formed\") at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_205330_create_inventory_logs_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#47 {main}
"} 
[2025-07-13 22:39:16] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`sale_items` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `sale_items` add constraint `sale_items_sale_id_foreign` foreign key (`sale_id`) references `sales` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`sale_items` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `sale_items` add constraint `sale_items_sale_id_foreign` foreign key (`sale_id`) references `sales` (`id`) on delete cascade) at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_205338_create_sale_items_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#45 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`sale_items` (errno: 150 \"Foreign key constraint is incorrectly formed\") at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_205338_create_sale_items_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#47 {main}
"} 
[2025-07-13 22:39:42] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`payments` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `payments` add constraint `payments_subscription_id_foreign` foreign key (`subscription_id`) references `subscriptions` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`payments` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `payments` add constraint `payments_subscription_id_foreign` foreign key (`subscription_id`) references `subscriptions` (`id`) on delete cascade) at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_205339_create_payments_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#45 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`payments` (errno: 150 \"Foreign key constraint is incorrectly formed\") at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_205339_create_payments_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#47 {main}
"} 
[2025-07-13 22:40:38] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'permissions' already exists (Connection: mysql, SQL: create table `permissions` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `guard_name` varchar(255) not null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'permissions' already exists (Connection: mysql, SQL: create table `permissions` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(255) not null, `guard_name` varchar(255) not null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_210840_create_permission_tables.php(23): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#45 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'permissions' already exists at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_210840_create_permission_tables.php(23): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#47 {main}
"} 
[2025-07-13 22:40:55] local.ERROR: SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_login_at' (Connection: mysql, SQL: alter table `users` add `last_login_at` timestamp null after `email_verified_at`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S21): SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_login_at' (Connection: mysql, SQL: alter table `users` add `last_login_at` timestamp null after `email_verified_at`) at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_223446_add_security_fields_to_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#45 {main}

[previous exception] [object] (PDOException(code: 42S21): SQLSTATE[42S21]: Column already exists: 1060 Duplicate column name 'last_login_at' at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_223446_add_security_fields_to_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#47 {main}
"} 
[2025-07-13 22:43:17] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'name' in 'where clause' (Connection: mysql, SQL: select * from `roles` where (`name` = super_admin) limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'name' in 'where clause' (Connection: mysql, SQL: select * from `roles` where (`name` = super_admin) limit 1) at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(872): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(854): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(678): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->firstOrCreate()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\SecuritySeeder.php(50): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\SecuritySeeder.php(20): Database\\Seeders\\SecuritySeeder->createRoles()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\SecuritySeeder->run()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#47 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#48 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'name' in 'where clause' at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(872): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(854): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(678): Illuminate\\Database\\Eloquent\\Builder->first()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->firstOrCreate()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\SecuritySeeder.php(50): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\SecuritySeeder.php(20): Database\\Seeders\\SecuritySeeder->createRoles()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\SecuritySeeder->run()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#47 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#48 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#49 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#50 {main}
"} 
[2025-07-13 22:44:07] local.ERROR: Class "Redis" not found {"exception":"[object] (Error(code: 0): Class \"Redis\" not found at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php:79)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(33): Illuminate\\Redis\\Connectors\\PhpRedisConnector->createClient()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\Connectors\\PhpRedisConnector.php(38): Illuminate\\Redis\\Connectors\\PhpRedisConnector->Illuminate\\Redis\\Connectors\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(109): Illuminate\\Redis\\Connectors\\PhpRedisConnector->connect()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Redis\\RedisManager.php(90): Illuminate\\Redis\\RedisManager->resolve()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RedisStore.php(356): Illuminate\\Redis\\RedisManager->connection()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\RedisStore.php(259): Illuminate\\Cache\\RedisStore->connection()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php(539): Illuminate\\Cache\\RedisStore->forget()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php(143): Illuminate\\Cache\\Repository->forget()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\spatie\\laravel-permission\\src\\Traits\\RefreshesPermissionCache.php(12): Spatie\\Permission\\PermissionRegistrar->forgetCachedPermissions()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(461): Spatie\\Permission\\Models\\Role::Spatie\\Permission\\Traits\\{closure}()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(288): Illuminate\\Events\\Dispatcher->Illuminate\\Events\\{closure}()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Events\\Dispatcher.php(268): Illuminate\\Events\\Dispatcher->invokeListeners()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasEvents.php(224): Illuminate\\Events\\Dispatcher->dispatch()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1274): Illuminate\\Database\\Eloquent\\Model->fireModelEvent()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1247): Illuminate\\Database\\Eloquent\\Model->finishSave()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(695): Illuminate\\Database\\Eloquent\\Builder->create()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1929): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(695): Illuminate\\Database\\Eloquent\\Builder->withSavepointIfNeeded()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(682): Illuminate\\Database\\Eloquent\\Builder->createOrFirst()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->firstOrCreate()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\SecuritySeeder.php(50): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\SecuritySeeder.php(20): Database\\Seeders\\SecuritySeeder->createRoles()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\SecuritySeeder->run()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#47 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#48 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#49 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#50 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#51 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#52 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#53 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#54 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#55 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#56 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(121): Illuminate\\Console\\Command->call()
#57 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(97): Illuminate\\Database\\Console\\Migrations\\FreshCommand->runSeeder()
#58 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#59 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#60 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#61 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#62 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#63 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#64 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#65 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#66 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#67 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#68 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#69 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#70 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#71 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#72 {main}
"} 
[2025-07-13 22:44:34] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'role_has_permissions.role_id' in 'field list' (Connection: mysql, SQL: select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'role_has_permissions.role_id' in 'field list' (Connection: mysql, SQL: select `permissions`.*, `role_has_permissions`.`role_id` as `pivot_role_id`, `role_has_permissions`.`permission_id` as `pivot_permission_id` from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `role_has_permissions`.`role_id` = 1) at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(872): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany.php(910): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany.php(894): Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany->get()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(634): Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany->getResults()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(572): Illuminate\\Database\\Eloquent\\Model->getRelationshipFromMethod()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(493): Illuminate\\Database\\Eloquent\\Model->getRelationValue()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2405): Illuminate\\Database\\Eloquent\\Model->getAttribute()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php(404): Illuminate\\Database\\Eloquent\\Model->__get()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\SecuritySeeder.php(151): Spatie\\Permission\\Models\\Role->givePermissionTo()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\SecuritySeeder.php(26): Database\\Seeders\\SecuritySeeder->assignPermissionsToRoles()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\SecuritySeeder->run()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#47 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#48 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#49 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'role_has_permissions.role_id' in 'field list' at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(872): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany.php(910): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany.php(894): Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany->get()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(634): Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany->getResults()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(572): Illuminate\\Database\\Eloquent\\Model->getRelationshipFromMethod()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(493): Illuminate\\Database\\Eloquent\\Model->getRelationValue()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2405): Illuminate\\Database\\Eloquent\\Model->getAttribute()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php(404): Illuminate\\Database\\Eloquent\\Model->__get()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\SecuritySeeder.php(151): Spatie\\Permission\\Models\\Role->givePermissionTo()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\SecuritySeeder.php(26): Database\\Seeders\\SecuritySeeder->assignPermissionsToRoles()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\SecuritySeeder->run()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#47 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#48 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#49 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#50 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#51 {main}
"} 
[2025-07-13 22:45:43] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`role_has_permissions` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `role_has_permissions` add constraint `role_has_permissions_role_id_foreign` foreign key (`role_id`) references `roles` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`role_has_permissions` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `role_has_permissions` add constraint `role_has_permissions_role_id_foreign` foreign key (`role_id`) references `roles` (`id`) on delete cascade) at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_205321_create_role_has_permissions_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#45 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`role_has_permissions` (errno: 150 \"Foreign key constraint is incorrectly formed\") at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_205321_create_role_has_permissions_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#47 {main}
"} 
[2025-07-13 22:46:20] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'users.deleted_at' in 'where clause' (Connection: mysql, SQL: select * from `users` where (`email` = <EMAIL>) and `users`.`deleted_at` is null limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'users.deleted_at' in 'where clause' (Connection: mysql, SQL: select * from `users` where (`email` = <EMAIL>) and `users`.`deleted_at` is null limit 1) at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(872): Illuminate\\Database\\Query\\Builder->get()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(854): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Eloquent\\Builder->get()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(678): Illuminate\\Database\\Eloquent\\Builder->first()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->firstOrCreate()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\SecuritySeeder.php(213): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\SecuritySeeder.php(29): Database\\Seeders\\SecuritySeeder->createSuperAdmin()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\SecuritySeeder->run()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(121): Illuminate\\Console\\Command->call()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(97): Illuminate\\Database\\Console\\Migrations\\FreshCommand->runSeeder()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#47 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#48 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#49 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#50 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#51 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#52 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#53 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#54 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#55 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#56 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#57 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#58 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#59 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#60 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'users.deleted_at' in 'where clause' at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:404)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): PDO->prepare()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3131): Illuminate\\Database\\Connection->select()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(872): Illuminate\\Database\\Query\\Builder->get()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(854): Illuminate\\Database\\Eloquent\\Builder->getModels()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(366): Illuminate\\Database\\Eloquent\\Builder->get()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(678): Illuminate\\Database\\Eloquent\\Builder->first()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->firstOrCreate()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2517): Illuminate\\Database\\Eloquent\\Model->forwardCallTo()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2533): Illuminate\\Database\\Eloquent\\Model->__call()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\SecuritySeeder.php(213): Illuminate\\Database\\Eloquent\\Model::__callStatic()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\SecuritySeeder.php(29): Database\\Seeders\\SecuritySeeder->createSuperAdmin()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\SecuritySeeder->run()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(121): Illuminate\\Console\\Command->call()
#47 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(97): Illuminate\\Database\\Console\\Migrations\\FreshCommand->runSeeder()
#48 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#49 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#50 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#51 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#52 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#53 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#54 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#55 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#56 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#57 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#58 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#59 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#60 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#61 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#62 {main}
"} 
