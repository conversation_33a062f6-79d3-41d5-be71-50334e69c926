[2025-07-13 22:37:41] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`users` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `users` add constraint `users_company_id_foreign` foreign key (`company_id`) references `companies` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`users` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `users` add constraint `users_company_id_foreign` foreign key (`company_id`) references `companies` (`id`) on delete cascade) at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`users` (errno: 150 \"Foreign key constraint is incorrectly formed\") at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\0001_01_01_000000_create_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#36 {main}
"} 
[2025-07-13 22:38:45] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`inventory_logs` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `inventory_logs` add constraint `inventory_logs_product_id_foreign` foreign key (`product_id`) references `products` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`inventory_logs` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `inventory_logs` add constraint `inventory_logs_product_id_foreign` foreign key (`product_id`) references `products` (`id`) on delete cascade) at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_205330_create_inventory_logs_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#45 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`inventory_logs` (errno: 150 \"Foreign key constraint is incorrectly formed\") at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_205330_create_inventory_logs_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#47 {main}
"} 
[2025-07-13 22:39:16] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`sale_items` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `sale_items` add constraint `sale_items_sale_id_foreign` foreign key (`sale_id`) references `sales` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`sale_items` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `sale_items` add constraint `sale_items_sale_id_foreign` foreign key (`sale_id`) references `sales` (`id`) on delete cascade) at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_205338_create_sale_items_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#45 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`sale_items` (errno: 150 \"Foreign key constraint is incorrectly formed\") at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_205338_create_sale_items_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#47 {main}
"} 
[2025-07-13 22:39:42] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`payments` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `payments` add constraint `payments_subscription_id_foreign` foreign key (`subscription_id`) references `subscriptions` (`id`) on delete cascade) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`payments` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `payments` add constraint `payments_subscription_id_foreign` foreign key (`subscription_id`) references `subscriptions` (`id`) on delete cascade) at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_205339_create_payments_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#45 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `wisaq_pos`.`payments` (errno: 150 \"Foreign key constraint is incorrectly formed\") at C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#2 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement()
#5 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build()
#7 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create()
#8 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\database\\migrations\\2025_07_13_205339_create_payments_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic()
#9 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod()
#11 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration()
#13 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render()
#15 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write()
#16 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp()
#17 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending()
#18 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run()
#19 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection()
#21 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#25 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#26 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#27 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#28 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#29 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#30 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run()
#31 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand()
#32 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call()
#33 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#36 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#37 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#38 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#39 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#40 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#41 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#42 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#43 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#44 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#45 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#46 C:\\xampp\\htdocs\\Wisaq\\wisaq-pos-backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#47 {main}
"} 
