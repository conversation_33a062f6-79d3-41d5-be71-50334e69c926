<?php

namespace App\Console\Commands;

use App\Models\AuditLog;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CleanupSecurityLogs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'security:cleanup-logs {--days=90 : Number of days to keep logs}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old security and audit logs';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $days = $this->option('days');
        $cutoffDate = now()->subDays($days);

        $this->info("تنظيف سجلات الأمان الأقدم من {$days} يوم...");

        try {
            // Clean up audit logs
            $auditLogsDeleted = AuditLog::where('created_at', '<', $cutoffDate)->count();
            AuditLog::where('created_at', '<', $cutoffDate)->delete();

            // Clean up failed login attempts cache
            $this->cleanupFailedAttempts();

            // Clean up rate limiting cache
            $this->cleanupRateLimitCache();

            // Clean up temporary IP blocks
            $this->cleanupIPBlocks();

            $this->info("تم حذف {$auditLogsDeleted} سجل من سجلات المراجعة");
            $this->info('تم تنظيف ذاكرة التخزين المؤقت للأمان');

            Log::info('Security logs cleanup completed', [
                'audit_logs_deleted' => $auditLogsDeleted,
                'retention_days' => $days,
                'executed_at' => now()
            ]);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('فشل في تنظيف سجلات الأمان: ' . $e->getMessage());
            Log::error('Security logs cleanup failed: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    /**
     * Clean up failed login attempts cache
     */
    protected function cleanupFailedAttempts(): void
    {
        $pattern = 'failed_attempts:*';
        $keys = \Cache::getRedis()->keys($pattern);
        
        if (!empty($keys)) {
            \Cache::getRedis()->del($keys);
        }
    }

    /**
     * Clean up rate limiting cache
     */
    protected function cleanupRateLimitCache(): void
    {
        $pattern = 'rate_limit:*';
        $keys = \Cache::getRedis()->keys($pattern);
        
        if (!empty($keys)) {
            \Cache::getRedis()->del($keys);
        }
    }

    /**
     * Clean up temporary IP blocks
     */
    protected function cleanupIPBlocks(): void
    {
        $pattern = 'ip_blocked:*';
        $keys = \Cache::getRedis()->keys($pattern);
        
        foreach ($keys as $key) {
            $blockedUntil = \Cache::get($key);
            if ($blockedUntil && $blockedUntil < time()) {
                \Cache::forget($key);
            }
        }
    }
}
