<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckSubscription
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        // Skip check for super admin
        if ($user && $user->isSuperAdmin()) {
            return $next($request);
        }

        // Check if user has a company
        if (!$user || !$user->company) {
            return response()->json([
                'success' => false,
                'message' => 'لا يوجد شركة مرتبطة بالحساب',
                'error_code' => 'NO_COMPANY'
            ], 403);
        }

        $company = $user->company;

        // Check if company is active
        if ($company->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'الشركة غير نشطة. يرجى التواصل مع الإدارة',
                'error_code' => 'COMPANY_INACTIVE'
            ], 403);
        }

        // Check subscription
        $subscription = $company->subscription;

        if (!$subscription) {
            return response()->json([
                'success' => false,
                'message' => 'لا يوجد اشتراك نشط',
                'error_code' => 'NO_SUBSCRIPTION'
            ], 403);
        }

        // Check if subscription is expired
        if ($subscription->expires_at && $subscription->expires_at->isPast()) {
            // Allow grace period of 3 days
            $gracePeriodEnd = $subscription->expires_at->addDays(3);
            
            if (now()->isAfter($gracePeriodEnd)) {
                // Suspend company after grace period
                $company->update(['status' => 'suspended']);
                
                return response()->json([
                    'success' => false,
                    'message' => 'انتهى الاشتراك. يرجى التجديد لاستمرار الخدمة',
                    'error_code' => 'SUBSCRIPTION_EXPIRED',
                    'data' => [
                        'expired_at' => $subscription->expires_at,
                        'grace_period_end' => $gracePeriodEnd
                    ]
                ], 402); // Payment Required
            } else {
                // Within grace period - add warning header
                $response = $next($request);
                $response->headers->set('X-Subscription-Warning', 'Subscription expired - grace period active');
                $response->headers->set('X-Grace-Period-End', $gracePeriodEnd->toISOString());
                return $response;
            }
        }

        // Check if subscription is cancelled but still active
        if ($subscription->status === 'cancelled' && $subscription->expires_at && $subscription->expires_at->isFuture()) {
            $response = $next($request);
            $response->headers->set('X-Subscription-Warning', 'Subscription cancelled - active until ' . $subscription->expires_at->format('Y-m-d'));
            return $response;
        }

        // Check if subscription is inactive
        if ($subscription->status !== 'active') {
            return response()->json([
                'success' => false,
                'message' => 'الاشتراك غير نشط',
                'error_code' => 'SUBSCRIPTION_INACTIVE',
                'data' => [
                    'status' => $subscription->status,
                    'expires_at' => $subscription->expires_at
                ]
            ], 402);
        }

        return $next($request);
    }
}
