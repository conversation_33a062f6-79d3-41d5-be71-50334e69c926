<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Branch;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    /**
     * عرض قائمة المستخدمين
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $branchId = Auth::user()->branch_id;
            $perPage = $request->get('per_page', 15);

            $query = User::with(['branch', 'roles'])
                ->where('company_id', $companyId);

            // فلترة حسب الفرع (إذا لم يكن مدير عام)
            if (!Auth::user()->isSuperAdmin() && $branchId) {
                $query->where('branch_id', $branchId);
            } elseif ($request->has('branch_id') && $request->branch_id) {
                $query->where('branch_id', $request->branch_id);
            }

            // فلترة حسب النوع
            if ($request->has('user_type') && $request->user_type) {
                $query->where('user_type', $request->user_type);
            }

            // فلترة حسب الحالة
            if ($request->has('status') && $request->status) {
                $query->where('status', $request->status);
            }

            // البحث
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%");
                });
            }

            // الترتيب
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            $users = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => [
                    'users' => $users->items(),
                    'pagination' => [
                        'current_page' => $users->currentPage(),
                        'last_page' => $users->lastPage(),
                        'per_page' => $users->perPage(),
                        'total' => $users->total(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب المستخدمين'
            ], 500);
        }
    }

    /**
     * إنشاء مستخدم جديد
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'phone' => 'nullable|string|max:20',
                'password' => 'required|string|min:6',
                'user_type' => 'required|in:admin,supervisor,manager,cashier,accountant',
                'branch_id' => 'required|exists:branches,id',
                'role' => 'required|string|exists:roles,name',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $companyId = Auth::user()->company_id;

            // التحقق من أن الفرع ينتمي للشركة
            $branch = Branch::where('id', $request->branch_id)
                ->where('company_id', $companyId)
                ->first();

            if (!$branch) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفرع المحدد غير موجود'
                ], 404);
            }

            $data = $request->except(['role']);
            $data['company_id'] = $companyId;
            $data['password'] = Hash::make($request->password);
            $data['status'] = 'active';

            $user = User::create($data);

            // تعيين الدور
            $user->assignRole($request->role);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء المستخدم بنجاح',
                'data' => $user->load(['branch', 'roles'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء المستخدم'
            ], 500);
        }
    }

    /**
     * عرض مستخدم محدد
     */
    public function show($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $user = User::with(['branch', 'roles', 'permissions'])
                ->where('company_id', $companyId)
                ->find($id);

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'المستخدم غير موجود'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $user
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب بيانات المستخدم'
            ], 500);
        }
    }

    /**
     * تحديث مستخدم
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $user = User::where('company_id', $companyId)->find($id);

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'المستخدم غير موجود'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email,' . $id,
                'phone' => 'nullable|string|max:20',
                'password' => 'nullable|string|min:6',
                'user_type' => 'required|in:admin,supervisor,manager,cashier,accountant',
                'branch_id' => 'required|exists:branches,id',
                'role' => 'nullable|string|exists:roles,name',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            // التحقق من أن الفرع ينتمي للشركة
            $branch = Branch::where('id', $request->branch_id)
                ->where('company_id', $companyId)
                ->first();

            if (!$branch) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفرع المحدد غير موجود'
                ], 404);
            }

            $data = $request->except(['role', 'password']);

            // تحديث كلمة المرور إذا تم إرسالها
            if ($request->password) {
                $data['password'] = Hash::make($request->password);
            }

            $user->update($data);

            // تحديث الدور إذا تم إرساله
            if ($request->role) {
                $user->syncRoles([$request->role]);
            }

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث المستخدم بنجاح',
                'data' => $user->load(['branch', 'roles'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث المستخدم'
            ], 500);
        }
    }

    /**
     * حذف مستخدم
     */
    public function destroy($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $user = User::where('company_id', $companyId)->find($id);

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'المستخدم غير موجود'
                ], 404);
            }

            // منع حذف المستخدم الحالي
            if ($user->id === Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكنك حذف حسابك الخاص'
                ], 400);
            }

            // التحقق من وجود مبيعات للمستخدم
            if ($user->sales()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف المستخدم لوجود مبيعات مرتبطة به'
                ], 400);
            }

            $user->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف المستخدم بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في حذف المستخدم'
            ], 500);
        }
    }

    /**
     * تبديل حالة المستخدم
     */
    public function toggleStatus($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $user = User::where('company_id', $companyId)->find($id);

            if (!$user) {
                return response()->json([
                    'success' => false,
                    'message' => 'المستخدم غير موجود'
                ], 404);
            }

            // منع تعطيل المستخدم الحالي
            if ($user->id === Auth::id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكنك تعطيل حسابك الخاص'
                ], 400);
            }

            $newStatus = $user->status === 'active' ? 'inactive' : 'active';
            $user->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'message' => $newStatus === 'active' ? 'تم تفعيل المستخدم' : 'تم تعطيل المستخدم',
                'data' => $user
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تغيير حالة المستخدم'
            ], 500);
        }
    }

    /**
     * الحصول على الأدوار المتاحة
     */
    public function getRoles(): JsonResponse
    {
        try {
            $roles = Role::where('name', '!=', 'super_admin')->get();

            return response()->json([
                'success' => true,
                'data' => $roles
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب الأدوار'
            ], 500);
        }
    }
}
