<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class IPSecurityMiddleware
{
    /**
     * Maximum failed attempts before blocking IP
     */
    protected $maxFailedAttempts = 10;

    /**
     * Block duration in minutes
     */
    protected $blockDuration = 60;

    /**
     * Whitelist of allowed IPs (can be configured in .env)
     */
    protected $whitelist = [];

    /**
     * Blacklist of blocked IPs
     */
    protected $blacklist = [];

    public function __construct()
    {
        // Load IP lists from config
        $this->whitelist = config('security.ip_whitelist', []);
        $this->blacklist = config('security.ip_blacklist', []);
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $ip = $this->getClientIP($request);

        // Check if IP is in blacklist
        if ($this->isBlacklisted($ip)) {
            Log::warning('Blocked IP attempted access', [
                'ip' => $ip,
                'url' => $request->fullUrl(),
                'user_agent' => $request->userAgent(),
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'الوصول مرفوض من هذا العنوان',
                'error_code' => 'IP_BLOCKED'
            ], 403);
        }

        // Check if IP is temporarily blocked due to failed attempts
        if ($this->isTemporarilyBlocked($ip)) {
            $remainingTime = $this->getRemainingBlockTime($ip);
            
            Log::warning('Temporarily blocked IP attempted access', [
                'ip' => $ip,
                'remaining_time' => $remainingTime,
                'url' => $request->fullUrl(),
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => false,
                'message' => "تم حظر الوصول مؤقتاً. يرجى المحاولة بعد {$remainingTime} دقيقة",
                'error_code' => 'IP_TEMPORARILY_BLOCKED',
                'retry_after' => $remainingTime * 60
            ], 429);
        }

        // Check for suspicious activity patterns
        if ($this->isSuspiciousActivity($request, $ip)) {
            $this->recordFailedAttempt($ip);
            
            Log::warning('Suspicious activity detected', [
                'ip' => $ip,
                'url' => $request->fullUrl(),
                'user_agent' => $request->userAgent(),
                'method' => $request->method(),
                'timestamp' => now()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'تم اكتشاف نشاط مشبوه',
                'error_code' => 'SUSPICIOUS_ACTIVITY'
            ], 403);
        }

        $response = $next($request);

        // Record failed login attempts
        if ($this->isFailedLoginAttempt($request, $response)) {
            $this->recordFailedAttempt($ip);
        }

        return $response;
    }

    /**
     * Get real client IP address
     */
    protected function getClientIP(Request $request): string
    {
        $headers = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_CLIENT_IP',            // Proxy
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];

        foreach ($headers as $header) {
            if (!empty($_SERVER[$header])) {
                $ips = explode(',', $_SERVER[$header]);
                $ip = trim($ips[0]);
                
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $request->ip();
    }

    /**
     * Check if IP is in blacklist
     */
    protected function isBlacklisted(string $ip): bool
    {
        return in_array($ip, $this->blacklist);
    }

    /**
     * Check if IP is temporarily blocked
     */
    protected function isTemporarilyBlocked(string $ip): bool
    {
        $key = "ip_blocked:{$ip}";
        return Cache::has($key);
    }

    /**
     * Get remaining block time in minutes
     */
    protected function getRemainingBlockTime(string $ip): int
    {
        $key = "ip_blocked:{$ip}";
        $blockedUntil = Cache::get($key);
        
        if ($blockedUntil) {
            return max(0, ceil(($blockedUntil - time()) / 60));
        }
        
        return 0;
    }

    /**
     * Record failed attempt
     */
    protected function recordFailedAttempt(string $ip): void
    {
        $key = "failed_attempts:{$ip}";
        $attempts = Cache::get($key, 0) + 1;
        
        Cache::put($key, $attempts, now()->addHour());
        
        // Block IP if too many failed attempts
        if ($attempts >= $this->maxFailedAttempts) {
            $this->blockIP($ip);
        }
    }

    /**
     * Block IP temporarily
     */
    protected function blockIP(string $ip): void
    {
        $key = "ip_blocked:{$ip}";
        $blockedUntil = time() + ($this->blockDuration * 60);
        
        Cache::put($key, $blockedUntil, now()->addMinutes($this->blockDuration));
        
        Log::warning('IP temporarily blocked due to failed attempts', [
            'ip' => $ip,
            'duration' => $this->blockDuration,
            'timestamp' => now()
        ]);
    }

    /**
     * Check for suspicious activity patterns
     */
    protected function isSuspiciousActivity(Request $request, string $ip): bool
    {
        // Check request rate
        $rateKey = "request_rate:{$ip}";
        $requests = Cache::get($rateKey, 0) + 1;
        Cache::put($rateKey, $requests, now()->addMinute());
        
        // More than 100 requests per minute is suspicious
        if ($requests > 100) {
            return true;
        }

        // Check for common attack patterns in URL
        $url = $request->fullUrl();
        $suspiciousPatterns = [
            '/wp-admin',
            '/phpmyadmin',
            '/.env',
            '/config',
            '/admin',
            '/login.php',
            '/wp-login.php',
            '/.git',
            '/backup',
            '/sql',
            '/database'
        ];

        foreach ($suspiciousPatterns as $pattern) {
            if (str_contains($url, $pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if this was a failed login attempt
     */
    protected function isFailedLoginAttempt(Request $request, Response $response): bool
    {
        return $request->is('api/auth/login') && 
               $response->getStatusCode() === 401;
    }
}
