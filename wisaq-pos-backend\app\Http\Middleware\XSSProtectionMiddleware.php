<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class XSSProtectionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Clean all input data
        $input = $request->all();
        $cleanInput = $this->cleanInput($input);
        $request->merge($cleanInput);

        $response = $next($request);

        // Add security headers
        $response->headers->set('X-Content-Type-Options', 'nosniff');
        $response->headers->set('X-Frame-Options', 'DENY');
        $response->headers->set('X-XSS-Protection', '1; mode=block');
        $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->headers->set('Content-Security-Policy', "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; media-src 'self'; object-src 'none'; child-src 'none'; worker-src 'none'; frame-ancestors 'none'; form-action 'self'; base-uri 'self'; manifest-src 'self'");

        return $response;
    }

    /**
     * Clean input data recursively
     */
    protected function cleanInput($input)
    {
        if (is_array($input)) {
            return array_map([$this, 'cleanInput'], $input);
        }

        if (is_string($input)) {
            // Remove potentially dangerous HTML tags and attributes
            $input = strip_tags($input, '<p><br><strong><em><u><ol><ul><li><h1><h2><h3><h4><h5><h6>');
            
            // Remove javascript: and data: protocols
            $input = preg_replace('/javascript:/i', '', $input);
            $input = preg_replace('/data:/i', '', $input);
            
            // Remove on* event handlers
            $input = preg_replace('/on\w+\s*=/i', '', $input);
            
            // Clean up common XSS patterns
            $input = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $input);
            $input = preg_replace('/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/mi', '', $input);
            
            return trim($input);
        }

        return $input;
    }
}
