<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class CategoryController extends Controller
{
    /**
     * عرض قائمة الفئات
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $perPage = $request->get('per_page', 15);

            $query = Category::with(['products'])
                ->where('company_id', $companyId);

            // فلترة حسب الحالة
            if ($request->has('status') && $request->status) {
                if ($request->status === 'active') {
                    $query->where('status', 'active');
                } elseif ($request->status === 'inactive') {
                    $query->where('status', 'inactive');
                }
            }

            // البحث
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('name_en', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            // الترتيب
            $sortBy = $request->get('sort_by', 'sort_order');
            $sortOrder = $request->get('sort_order', 'asc');
            $query->orderBy($sortBy, $sortOrder);

            if ($request->has('paginate') && $request->paginate === 'false') {
                $categories = $query->get();
                return response()->json([
                    'success' => true,
                    'data' => $categories
                ]);
            }

            $categories = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => [
                    'categories' => $categories->items(),
                    'pagination' => [
                        'current_page' => $categories->currentPage(),
                        'last_page' => $categories->lastPage(),
                        'per_page' => $categories->perPage(),
                        'total' => $categories->total(),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب الفئات'
            ], 500);
        }
    }

    /**
     * إنشاء فئة جديدة
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'name_en' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'description_en' => 'nullable|string',
                'color' => 'required|string|max:7',
                'sort_order' => 'nullable|integer|min:0',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $companyId = Auth::user()->company_id;

            $data = $request->all();
            $data['company_id'] = $companyId;
            $data['status'] = 'active';

            // تحديد ترتيب الفئة
            if (!isset($data['sort_order'])) {
                $maxOrder = Category::where('company_id', $companyId)->max('sort_order');
                $data['sort_order'] = ($maxOrder ?? 0) + 1;
            }

            // رفع الصورة
            if ($request->hasFile('image')) {
                $image = $request->file('image');
                $imageName = time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
                $imagePath = $image->storeAs('categories', $imageName, 'public');
                $data['image'] = $imagePath;
            }

            $category = Category::create($data);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء الفئة بنجاح',
                'data' => $category
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء الفئة'
            ], 500);
        }
    }

    /**
     * عرض فئة محددة
     */
    public function show($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $category = Category::with(['products' => function($query) {
                $query->where('is_active', true)->limit(10);
            }])
                ->where('company_id', $companyId)
                ->find($id);

            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفئة غير موجودة'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $category
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب بيانات الفئة'
            ], 500);
        }
    }

    /**
     * تحديث فئة
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $category = Category::where('company_id', $companyId)->find($id);

            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفئة غير موجودة'
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'name_en' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'description_en' => 'nullable|string',
                'color' => 'required|string|max:7',
                'sort_order' => 'nullable|integer|min:0',
                'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'بيانات غير صحيحة',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $request->except(['image']);

            // رفع الصورة الجديدة
            if ($request->hasFile('image')) {
                // حذف الصورة القديمة
                if ($category->image) {
                    Storage::disk('public')->delete($category->image);
                }

                $image = $request->file('image');
                $imageName = time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
                $imagePath = $image->storeAs('categories', $imageName, 'public');
                $data['image'] = $imagePath;
            }

            $category->update($data);

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث الفئة بنجاح',
                'data' => $category
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تحديث الفئة'
            ], 500);
        }
    }

    /**
     * حذف فئة
     */
    public function destroy($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $category = Category::where('company_id', $companyId)->find($id);

            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفئة غير موجودة'
                ], 404);
            }

            // التحقق من وجود منتجات في الفئة
            if ($category->products()->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'لا يمكن حذف الفئة لوجود منتجات مرتبطة بها'
                ], 400);
            }

            // حذف الصورة
            if ($category->image) {
                Storage::disk('public')->delete($category->image);
            }

            $category->delete();

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الفئة بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في حذف الفئة'
            ], 500);
        }
    }

    /**
     * تبديل حالة الفئة
     */
    public function toggleStatus($id): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;

            $category = Category::where('company_id', $companyId)->find($id);

            if (!$category) {
                return response()->json([
                    'success' => false,
                    'message' => 'الفئة غير موجودة'
                ], 404);
            }

            $newStatus = $category->status === 'active' ? 'inactive' : 'active';
            $category->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'message' => $newStatus === 'active' ? 'تم تفعيل الفئة' : 'تم إلغاء تفعيل الفئة',
                'data' => $category
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في تغيير حالة الفئة'
            ], 500);
        }
    }
}
