<?php

namespace App\Http\Middleware;

use App\Models\AuditLog;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class AuditLogMiddleware
{
    /**
     * Actions that should be logged
     */
    protected $loggedActions = [
        'POST', 'PUT', 'PATCH', 'DELETE'
    ];

    /**
     * Routes that should be excluded from logging
     */
    protected $excludedRoutes = [
        'api/auth/refresh',
        'api/heartbeat',
        'api/health-check'
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only log specific HTTP methods
        if (!in_array($request->method(), $this->loggedActions)) {
            return $response;
        }

        // Skip excluded routes
        if ($this->shouldExclude($request)) {
            return $response;
        }

        // Log the activity
        $this->logActivity($request, $response);

        return $response;
    }

    /**
     * Check if route should be excluded from logging
     */
    protected function shouldExclude(Request $request): bool
    {
        $path = $request->path();
        
        foreach ($this->excludedRoutes as $excludedRoute) {
            if (str_contains($path, $excludedRoute)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Log the activity
     */
    protected function logActivity(Request $request, Response $response): void
    {
        try {
            $user = Auth::user();
            $statusCode = $response->getStatusCode();
            
            // Determine action type based on route and method
            $actionType = $this->determineActionType($request);
            
            // Get affected resource
            $affectedResource = $this->getAffectedResource($request);
            
            // Prepare request data (excluding sensitive information)
            $requestData = $this->sanitizeRequestData($request->all());
            
            AuditLog::create([
                'user_id' => $user ? $user->id : null,
                'company_id' => $user && $user->company_id ? $user->company_id : null,
                'branch_id' => $user && $user->branch_id ? $user->branch_id : null,
                'action_type' => $actionType,
                'resource_type' => $affectedResource['type'],
                'resource_id' => $affectedResource['id'],
                'description' => $this->generateDescription($actionType, $affectedResource, $user),
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'request_method' => $request->method(),
                'request_url' => $request->fullUrl(),
                'request_data' => json_encode($requestData),
                'response_status' => $statusCode,
                'success' => $statusCode >= 200 && $statusCode < 300,
                'created_at' => now()
            ]);
        } catch (\Exception $e) {
            // Don't let audit logging break the application
            \Log::error('Failed to create audit log: ' . $e->getMessage());
        }
    }

    /**
     * Determine action type based on request
     */
    protected function determineActionType(Request $request): string
    {
        $method = $request->method();
        $path = $request->path();

        // Map common patterns to action types
        if (str_contains($path, '/login')) return 'LOGIN';
        if (str_contains($path, '/logout')) return 'LOGOUT';
        if (str_contains($path, '/register')) return 'REGISTER';
        if (str_contains($path, '/password')) return 'PASSWORD_CHANGE';
        
        switch ($method) {
            case 'POST':
                return 'CREATE';
            case 'PUT':
            case 'PATCH':
                return 'UPDATE';
            case 'DELETE':
                return 'DELETE';
            default:
                return 'ACTION';
        }
    }

    /**
     * Get affected resource information
     */
    protected function getAffectedResource(Request $request): array
    {
        $path = $request->path();
        $segments = explode('/', $path);
        
        // Try to extract resource type and ID from URL
        if (count($segments) >= 2) {
            $resourceType = $segments[1]; // Usually api/resource/id
            $resourceId = null;
            
            // Look for numeric ID in segments
            foreach ($segments as $segment) {
                if (is_numeric($segment)) {
                    $resourceId = (int) $segment;
                    break;
                }
            }
            
            return [
                'type' => $resourceType,
                'id' => $resourceId
            ];
        }
        
        return [
            'type' => 'unknown',
            'id' => null
        ];
    }

    /**
     * Generate human-readable description
     */
    protected function generateDescription(string $actionType, array $resource, $user): string
    {
        $userName = $user ? $user->name : 'مستخدم غير معروف';
        $resourceType = $resource['type'];
        $resourceId = $resource['id'];
        
        $actions = [
            'CREATE' => 'أنشأ',
            'UPDATE' => 'حدث',
            'DELETE' => 'حذف',
            'LOGIN' => 'سجل دخول',
            'LOGOUT' => 'سجل خروج',
            'REGISTER' => 'سجل حساب جديد',
            'PASSWORD_CHANGE' => 'غير كلمة المرور'
        ];
        
        $action = $actions[$actionType] ?? 'نفذ عملية';
        
        if ($resourceId) {
            return "{$userName} {$action} {$resourceType} رقم {$resourceId}";
        }
        
        return "{$userName} {$action} {$resourceType}";
    }

    /**
     * Remove sensitive data from request data
     */
    protected function sanitizeRequestData(array $data): array
    {
        $sensitiveFields = [
            'password',
            'password_confirmation',
            'current_password',
            'new_password',
            'token',
            'api_key',
            'secret',
            'credit_card',
            'card_number',
            'cvv',
            'pin'
        ];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }
        
        return $data;
    }
}
