<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Sale;
use App\Models\Product;
use App\Models\Customer;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * إحصائيات لوحة التحكم
     */
    public function dashboardStats(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $branchId = Auth::user()->branch_id;
            $period = $request->get('period', 'today'); // today, week, month, year

            $query = Sale::where('company_id', $companyId)
                ->where('status', 'completed');

            // فلترة حسب الفرع إذا لم يكن مدير عام
            if (!Auth::user()->isSuperAdmin() && $branchId) {
                $query->where('branch_id', $branchId);
            }

            // فلترة حسب الفترة
            switch ($period) {
                case 'today':
                    $query->whereDate('sale_date', today());
                    break;
                case 'week':
                    $query->whereBetween('sale_date', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereMonth('sale_date', now()->month)
                          ->whereYear('sale_date', now()->year);
                    break;
                case 'year':
                    $query->whereYear('sale_date', now()->year);
                    break;
            }

            $sales = $query->get();

            // إحصائيات المبيعات
            $totalRevenue = $sales->sum('total_amount');
            $totalSales = $sales->count();
            $averageOrderValue = $totalSales > 0 ? $totalRevenue / $totalSales : 0;
            $totalTax = $sales->sum('tax_amount');
            $totalDiscount = $sales->sum('discount_amount');

            // أفضل المنتجات مبيعاً
            $topSellingProducts = DB::table('sale_items')
                ->join('sales', 'sale_items.sale_id', '=', 'sales.id')
                ->join('products', 'sale_items.product_id', '=', 'products.id')
                ->where('sales.company_id', $companyId)
                ->where('sales.status', 'completed')
                ->when($branchId && !Auth::user()->isSuperAdmin(), function($q) use ($branchId) {
                    return $q->where('sales.branch_id', $branchId);
                })
                ->when($period === 'today', function($q) {
                    return $q->whereDate('sales.sale_date', today());
                })
                ->when($period === 'week', function($q) {
                    return $q->whereBetween('sales.sale_date', [now()->startOfWeek(), now()->endOfWeek()]);
                })
                ->when($period === 'month', function($q) {
                    return $q->whereMonth('sales.sale_date', now()->month)
                             ->whereYear('sales.sale_date', now()->year);
                })
                ->when($period === 'year', function($q) {
                    return $q->whereYear('sales.sale_date', now()->year);
                })
                ->select(
                    'products.name as product_name',
                    'products.image as product_image',
                    DB::raw('SUM(sale_items.quantity) as total_quantity'),
                    DB::raw('SUM(sale_items.total) as total_revenue')
                )
                ->groupBy('products.id', 'products.name', 'products.image')
                ->orderBy('total_quantity', 'desc')
                ->limit(10)
                ->get();

            // إحصائيات المخزون
            $lowStockProducts = Product::where('company_id', $companyId)
                ->where('track_quantity', true)
                ->whereColumn('quantity', '<=', 'min_quantity')
                ->count();

            $outOfStockProducts = Product::where('company_id', $companyId)
                ->where('track_quantity', true)
                ->where('quantity', 0)
                ->count();

            // إحصائيات العملاء
            $totalCustomers = Customer::where('company_id', $companyId)->count();
            $newCustomersThisMonth = Customer::where('company_id', $companyId)
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count();

            // مبيعات آخر 7 أيام للرسم البياني
            $salesChart = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = now()->subDays($i);
                $dailySales = Sale::where('company_id', $companyId)
                    ->where('status', 'completed')
                    ->when($branchId && !Auth::user()->isSuperAdmin(), function($q) use ($branchId) {
                        return $q->where('branch_id', $branchId);
                    })
                    ->whereDate('sale_date', $date)
                    ->sum('total_amount');

                $salesChart[] = [
                    'date' => $date->format('Y-m-d'),
                    'day' => $date->format('D'),
                    'sales' => $dailySales
                ];
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'period' => $period,
                    'sales_stats' => [
                        'total_revenue' => $totalRevenue,
                        'total_sales' => $totalSales,
                        'average_order_value' => $averageOrderValue,
                        'total_tax' => $totalTax,
                        'total_discount' => $totalDiscount,
                    ],
                    'inventory_stats' => [
                        'low_stock_products' => $lowStockProducts,
                        'out_of_stock_products' => $outOfStockProducts,
                    ],
                    'customer_stats' => [
                        'total_customers' => $totalCustomers,
                        'new_customers_this_month' => $newCustomersThisMonth,
                    ],
                    'top_selling_products' => $topSellingProducts,
                    'sales_chart' => $salesChart,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في جلب إحصائيات لوحة التحكم'
            ], 500);
        }
    }

    /**
     * تقرير المبيعات المفصل
     */
    public function salesReport(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            $branchId = Auth::user()->branch_id;
            
            $dateFrom = $request->get('date_from', now()->startOfMonth()->format('Y-m-d'));
            $dateTo = $request->get('date_to', now()->format('Y-m-d'));
            $groupBy = $request->get('group_by', 'day'); // day, week, month

            $query = Sale::where('company_id', $companyId)
                ->where('status', 'completed')
                ->whereBetween('sale_date', [$dateFrom, $dateTo]);

            // فلترة حسب الفرع
            if (!Auth::user()->isSuperAdmin() && $branchId) {
                $query->where('branch_id', $branchId);
            } elseif ($request->has('branch_id') && $request->branch_id) {
                $query->where('branch_id', $request->branch_id);
            }

            $sales = $query->get();

            // تجميع البيانات
            $groupedSales = [];
            foreach ($sales as $sale) {
                $date = Carbon::parse($sale->sale_date);
                
                switch ($groupBy) {
                    case 'day':
                        $key = $date->format('Y-m-d');
                        $label = $date->format('M d, Y');
                        break;
                    case 'week':
                        $key = $date->format('Y-W');
                        $label = 'Week ' . $date->format('W, Y');
                        break;
                    case 'month':
                        $key = $date->format('Y-m');
                        $label = $date->format('M Y');
                        break;
                    default:
                        $key = $date->format('Y-m-d');
                        $label = $date->format('M d, Y');
                }

                if (!isset($groupedSales[$key])) {
                    $groupedSales[$key] = [
                        'period' => $label,
                        'total_sales' => 0,
                        'total_revenue' => 0,
                        'total_tax' => 0,
                        'total_discount' => 0,
                        'average_order_value' => 0,
                    ];
                }

                $groupedSales[$key]['total_sales']++;
                $groupedSales[$key]['total_revenue'] += $sale->total_amount;
                $groupedSales[$key]['total_tax'] += $sale->tax_amount;
                $groupedSales[$key]['total_discount'] += $sale->discount_amount;
            }

            // حساب متوسط قيمة الطلب
            foreach ($groupedSales as &$group) {
                $group['average_order_value'] = $group['total_sales'] > 0 
                    ? $group['total_revenue'] / $group['total_sales'] 
                    : 0;
            }

            // إجمالي التقرير
            $totalSales = $sales->count();
            $totalRevenue = $sales->sum('total_amount');
            $totalTax = $sales->sum('tax_amount');
            $totalDiscount = $sales->sum('discount_amount');
            $averageOrderValue = $totalSales > 0 ? $totalRevenue / $totalSales : 0;

            return response()->json([
                'success' => true,
                'data' => [
                    'period' => [
                        'from' => $dateFrom,
                        'to' => $dateTo,
                        'group_by' => $groupBy
                    ],
                    'summary' => [
                        'total_sales' => $totalSales,
                        'total_revenue' => $totalRevenue,
                        'total_tax' => $totalTax,
                        'total_discount' => $totalDiscount,
                        'average_order_value' => $averageOrderValue,
                    ],
                    'grouped_data' => array_values($groupedSales),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء تقرير المبيعات'
            ], 500);
        }
    }

    /**
     * تقرير المخزون
     */
    public function inventoryReport(Request $request): JsonResponse
    {
        try {
            $companyId = Auth::user()->company_id;
            
            $products = Product::with(['category'])
                ->where('company_id', $companyId)
                ->get();

            $totalProducts = $products->count();
            $activeProducts = $products->where('is_active', true)->count();
            $lowStockProducts = $products->where('track_quantity', true)
                ->filter(function($product) {
                    return $product->quantity <= $product->min_quantity;
                });
            $outOfStockProducts = $products->where('track_quantity', true)
                ->where('quantity', 0);

            $totalInventoryValue = $products->sum(function($product) {
                return $product->quantity * $product->cost_price;
            });

            $totalSellingValue = $products->sum(function($product) {
                return $product->quantity * $product->selling_price;
            });

            // تجميع حسب الفئات
            $categoryStats = $products->groupBy('category_id')->map(function($categoryProducts) {
                $category = $categoryProducts->first()->category;
                return [
                    'category_name' => $category ? $category->name : 'بدون فئة',
                    'total_products' => $categoryProducts->count(),
                    'total_quantity' => $categoryProducts->sum('quantity'),
                    'total_value' => $categoryProducts->sum(function($product) {
                        return $product->quantity * $product->cost_price;
                    }),
                ];
            })->values();

            return response()->json([
                'success' => true,
                'data' => [
                    'summary' => [
                        'total_products' => $totalProducts,
                        'active_products' => $activeProducts,
                        'low_stock_count' => $lowStockProducts->count(),
                        'out_of_stock_count' => $outOfStockProducts->count(),
                        'total_inventory_value' => $totalInventoryValue,
                        'total_selling_value' => $totalSellingValue,
                        'potential_profit' => $totalSellingValue - $totalInventoryValue,
                    ],
                    'low_stock_products' => $lowStockProducts->values(),
                    'out_of_stock_products' => $outOfStockProducts->values(),
                    'category_stats' => $categoryStats,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'خطأ في إنشاء تقرير المخزون'
            ], 500);
        }
    }
}
